#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

staged_ts_files=$(git diff --cached --name-only --diff-filter=ACMRTUXB)

IFS='
'

# 进入项目根目录
cd $(git rev-parse --show-toplevel 2> /dev/null || echo ".")

# 获取项目根目录的绝对路径
project_root=$(pwd)

# 项目根目录
echo "Project root directory: $project_root"
array=($staged_ts_files)
for file in "${array[@]}"; do
  if echo "$file" | grep -Eq '\.tsx?$'; then
    # 检查 tsc 文件是否存在并且可执行
    if [ -x "${project_root}/node_modules/typescript/bin/tsc" ]; then
      echo "check - $file"
      # 使用 tsc 检查暂存区中的 TypeScript 文件
      node "${project_root}/node_modules/typescript/bin/tsc" $file --noEmit --jsx react --esModuleInterop --skipLibCheck
      # 检查 tsc 命令的退出状态，如果非零，则说明有错误
      tsc_exit_code=$?
      if [ $tsc_exit_code -ne 0 ]; then
        echo "TypeScript compilation failed. Please fix the errors before committing."
        exit $tsc_exit_code
      fi
    else
      echo "没有 npm install, 跳过TS类型检查"
    fi
  fi
done

npm run lint