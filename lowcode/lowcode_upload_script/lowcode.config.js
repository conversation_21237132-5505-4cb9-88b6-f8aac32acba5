const path = require('path');
const uploadConfig = require('./upload.config');

module.exports = {
    mode: 'production',
    entry: {
        lowcode: path.resolve(__dirname, '../dist/index.js'),
    },
    output: {
        library: uploadConfig.lowcodeUmdOutputName,
        libraryTarget: 'umd',
        filename: 'lowcode.umd.js',
        umdNamedDefine: true,
        path: path.resolve(__dirname, '../dist'),
    },
    ignoreWarnings: [() => true],
    devServer: {
        hot: true,
        port: 8008,
        host: '127.0.0.1',
        historyApiFallback: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
        client: {
            reconnect: false,
        },
    },
};
