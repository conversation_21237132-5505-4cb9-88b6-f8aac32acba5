{
    "compilerOptions": {
        "target": "es2016" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
        "module": "commonjs" /* Specify what module code is generated. */,
        "strict": true,
        "skipLibCheck": true,
        "jsx": "preserve",
        "allowJs": true,
        "importHelpers": true,
        "moduleResolution": "node",
        "experimentalDecorators": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "sourceMap": true,
        "baseUrl": ".",
        "resolveJsonModule": true,
        "outDir": "../dist"
    },
    "exclude": ["./dist"],
    "include": ["../*"]
}
