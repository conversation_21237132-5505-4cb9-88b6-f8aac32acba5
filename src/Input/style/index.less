@prefix: ~'rbm';

:root {
    --rbm-input-padding: unset;
    --_rbm-input-padding: 14px 12px;

    --rbm-input-height: unset;
    --_rbm-input-height: 48px;

    --rbm-input-width: unset;
    --_rbm-input-width: 100%;

    --rbm-input-line-height: unset;
    --_rbm-input-line-height: 20px;

    --rbm-input-clear-padding-right: unset;
    --_rbm-input-clear-padding-right: 0;

    --rbm-input-disabled-opacity: unset;
    --_rbm-input-disabled-opacity: 1;

    --rbm-input-prefix-padding-right: unset;
    --_rbm-input-prefix-padding-right: 8px;

    --rbm-input-suffix-padding-right: unset;
    --_rbm-input-suffix-padding-right: 8px;

    --rbm-input-textarea-padding: unset;
    --_rbm-input-textarea-padding: 14px 12px;

    --rbm-input-textarea-clear-padding-right: unset;
    --_rbm-input-textarea-clear-padding-right: 12px;

    --rbm-input-search-wrapper-padding: unset;
    --_rbm-input-search-wrapper-padding: 6px 12px;

    --rbm-input-search-input-height: unset;
    --_rbm-input-search-input-height: 32px;

    --rbm-input-search-input-border-radius: unset;
    --_rbm-input-search-input-border-radius: 32px;
}

.@{prefix}-input {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: var(--rbm-input-width, var(--_rbm-input-width));
    padding: var(--rbm-input-padding, var(--_rbm-input-padding));
    line-height: var(--rbm-input-line-height, var(--_rbm-input-line-height));
    height: var(--rbm-input-height, var(--_rbm-input-height));
    text-align: left;
    background-color: var(--rbm-gray-6);
    box-sizing: border-box;
    font-size: var(--rbm-font-size-md);

    &--center {
        .@{prefix}-input__control {
            text-align: center;
        }
    }

    &--right {
        .@{prefix}-input__control {
            text-align: right;
        }
        .@{prefix}-input__clear {
            padding-right: var(--rbm-input-clear-padding-right, var(--_rbm-input-clear-padding-right));
        }
    }

    &__control {
        display: block;
        box-sizing: border-box;
        flex: 1;
        width: 100%;
        min-width: 0; // for flex-shrink in field__button
        margin: 0;
        padding: 0;
        color: var(--text-primary-color);
        font-size: inherit;
        line-height: inherit;
        text-align: inherit;
        background-color: inherit;
        border: 0;
        resize: none;
        // https://github.com/youzan/vant/pull/9418
        user-select: auto;
        &:focus {
            outline: none;
        }
        &::placeholder {
            color: var(--rbm-font-color-desc);
        }

        &:disabled {
            color: var(--rbm-font-color-comm);
            cursor: not-allowed;
            opacity: var(--rbm-input-disabled-opacity, var(--_rbm-input-clear-disabled-opacity));
            // fix disabled color in mobile safari
            -webkit-text-fill-color: var(--rbm-font-color-comm);
        }

        &:read-only {
            cursor: default;
        }

        // for ios wechat
        &[type='date'],
        &[type='time'],
        &[type='datetime-local'] {
            min-height: 24px;
        }

        // for safari
        &[type='search'] {
            -webkit-appearance: none;

            &::-webkit-search-cancel-button {
                -webkit-appearance: none;
            }
        }
    }

    &__clear {
        flex-shrink: 0;
        color: var(--rbm-font-color-desc);
        padding-left: 8px;
        box-sizing: content-box;
        cursor: pointer;
        font-size: var(--rbm-font-size-md);
        display: flex;
        align-items: center;
    }

    &__prefix,
    &__suffix {
        display: flex;
        align-items: center;
    }
    &__prefix {
        padding-right: var(--rbm-input-prefix-padding-right, var(--_rbm-input-prefix-padding-right));
    }
    &__suffix {
        padding-left: var(--rbm-input-suffix-padding-right, var(--_rbm-input-suffix-padding-right));
    }
}

.@{prefix}-input__transparent {
    background-color: transparent;
}

.@{prefix}-input--rtl {
    direction: rtl;
    text-align: right;

    &.@{prefix}-input {
        &--left {
            .@{prefix}-input__control {
                text-align: left;
            }
            .@{prefix}-input__clear {
                padding-left: 0;
            }
        }
    }

    .@{prefix}-input__clear {
        padding-left: 0;
        padding-right: 8px;
    }
    .@{prefix}-input__prefix {
        padding-left: 8px;
        padding-right: 0;
    }
    .@{prefix}-input__suffix {
        padding-left: 0;
        padding-right: 8px;
    }
}

.@{prefix}-textarea {
    position: relative;
    width: 100%;
    max-width: 100%;
    text-align: left;
    padding: var(--rbm-input-textarea-padding, var(--_rbm-input-textarea-padding));
    background-color: var(--rbm-gray-6);
    font-size: var(--rbm-font-size-md);
    box-sizing: border-box;

    .@{prefix}-textarea__control {
        display: block;
        box-sizing: border-box;
        width: 100%;
        min-width: 0; // for flex-shrink in field__button
        margin: 0;
        padding: 0;
        color: var(--rbm-font-color-text);
        font-size: inherit;
        line-height: 1.5;
        text-align: inherit;
        background-color: var(--rbm-gray-6);
        border: 0;
        resize: none;
        // https://github.com/youzan/vant/pull/9418
        user-select: auto;

        &:focus {
            outline: none;
        }
        &::placeholder {
            color: var(--rbm-font-color-desc);
        }

        &:disabled {
            color: var(--rbm-gray-2);
            cursor: not-allowed;
            opacity: 1;
            // fix disabled color in mobile safari
            -webkit-text-fill-color: var(--rbm-gray-2);
        }

        &:read-only {
            cursor: default;
        }

        &--min-height {
            min-height: 20px;
        }

        &--clear {
            padding-right: var(--rbm-input-textarea-clear-padding-right, var(--_rbm-input-textarea-clear-padding-right));
        }
    }

    .@{prefix}-textarea__clear {
        position: absolute;
        transform: translateY(-50%);
        top: 50%;
        right: 12px;
        z-index: 1;
        color: var(--rbm-font-color-desc);
        font-size: var(--rbm-font-size-md);
        cursor: pointer;
    }

    .@{prefix}-textarea__word-limit {
        // margin-top: var(--rv-padding-base);
        color: var(--rbm-font-color-desc);
        font-size: var(--rbm-font-size-s);
        line-height: 1.2;
        text-align: right;
    }
}

.@{prefix}-textarea--rtl {
    direction: rtl;
    text-align: right;

    .@{prefix}-textarea__control {
        &--clear {
            padding-left: 12px;
            padding-right: 0;
        }
    }

    .@{prefix}-textarea__clear {
        left: 12px;
        right: auto;
    }

    .@{prefix}-textarea__word-limit {
        text-align: left;
    }
}

.@{prefix}-search {
    &__wrapper {
        display: flex;
        align-items: center;
        padding: var(--rbm-input-search-wrapper-padding, var(--_rbm-input-search-wrapper-padding));
        background-color: var(--rbm-gray-6);

        .rbm-button-type-text {
            padding: 0;
            color: var(--rbm-font-color-text);
            font-size: var(--rbm-font-size-md);
            font-weight: 400;
        }
        .rbm-search-icon-size {
            font-size: var(--rbm-font-size-l);
        }
    }
    &__input {
        flex: 1;
        height: var(--rbm-input-search-input-height, var(--_rbm-input-search-input-height));
        background-color: var(--rbm-page-bg);
        border-radius: var(--rbm-input-search-input-border-radius, var(--_rbm-input-search-input-border-radius));
        margin-right: 12px;
    }
    &__button {
        font-size: var(--rbm-font-size-md);
    }
}

.@{prefix}-search--rtl {
    direction: rtl;
    .@{prefix}-search__input {
        margin-left: 12px;
        margin-right: 0;
    }
}

.@{prefix}-input-clear-icon {
    font-size: var(--rbm-font-size-md);
}
