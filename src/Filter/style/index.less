@import '../../var.less';
.text-center {
    text-align: center;
}
@filterPrefix: ~'rbm-filter';
@filterPanelPrefix: ~'rbm-filterPanel';

:root {
    // FilterPanel组件变量 - 未选中状态
    --rbm-filter-panel-option-background: var(--rbm-page-bg);

    --rbm-filter-panel-option-font-size: var(--rbm-font-size-md);

    --rbm-filter-panel-option-color: var(--rbm-font-color-sub);

    --rbm-filter-panel-option-font-weight: var(--rbm-font-weight-normal);

    // FilterPanel组件变量 - 选中状态
    --rbm-filter-panel-option-active-background: var(--rbm-color-primary-bg);

    --rbm-filter-panel-option-active-color: var(--rbm-color-warning);

    --rbm-filter-panel-option-active-font-weight: var(--rbm-font-weight-bold);

    // 间距
    --rbm-filter-panel-option-gap: 7.5px;

    --rbm-filter-panel-option-border-radius: var(--rbm-radius-2);
}

@keyframes rotateArrow {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(180deg);
    }
}

.@{filterPrefix} {
    &-search {
        &-wrapper {
            font-size: var(--rbm-font-size-s);
            color: var(--rbm-font-color-text);
            line-height: var(--rbm-line-height-xs);
            font-weight: var(--rbm-font-weight-normal);
            display: flex;
            align-items: center;
        }
        &-active {
            color: var(--rbm-color-warning);
            font-weight: var(--rbm-font-weight-bold);
            & > .@{filterPrefix}-search-icon {
                color: var(--rbm-color-warning) !important;
            }
        }
        &-disabled {
            opacity: 0.5;
        }
        &-icon {
            font-size: var(--rbm-font-size-s);
            transform: rotate(0deg);
            transition: transform 0.3s ease;
            &-active {
                animation: rotateArrow 0.3s ease forwards;
            }
        }

        &-popup {
            .rbm-popup-content {
                border-radius: 0 0 10.5px 10.5px;
            }
            .rbm-popup-top-show {
                animation: none;
            }
            .rbm-popup-top-hide {
                animation: none;
            }
        }
    }
    &-empty-wrapper {
        min-height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    &-rtl {
        direction: rtl;
    }
}

.@{filterPanelPrefix} {
    &-wrapper {
        padding: 12px 12px 0;
    }
    &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
    }
    &-icon {
        font-size: 12px;
        &-active {
            animation: rotateArrow 0.3s ease forwards;
        }
    }
    &-extra {
        font-size: var(--rbm-font-size-s);
        color: var(--rbm-font-color-sub);
        text-align: right;
        line-height: var(--rbm-line-height-xs);
        font-weight: var(--rbm-font-weight-normal);
        display: flex;
        align-items: center;
    }
    &-title {
        font-size: var(--rbm-font-size-md);
        color: #111111;
        letter-spacing: 0;
        line-height: var(--rbm-line-height-s);
        font-weight: var(--rbm-font-weight-bold);
    }
    &-content {
        max-height: 416px;
        overflow-y: scroll;
        & > div:not(:first-child) {
            margin-top: 18px;
        }
        &::-webkit-scrollbar {
            display: none; /* Chrome Safari */
        }
    }
    &-option-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: var(--rbm-filter-panel-option-gap);
    }
    &-option {
        padding: var(--rbm-space-4) var(--rbm-space-6);
        // border: 1px solid #eee;
        border-radius: var(--rbm-filter-panel-option-border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: left;
        line-height: var(--rbm-line-height-xs);
        box-sizing: border-box;
        background-color: var(--rbm-filter-panel-option-background);
        font-size: var(--rbm-filter-panel-option-font-size);
        color: var(--rbm-filter-panel-option-color);
        font-weight: var(--rbm-filter-panel-option-font-weight);
        &-active {
            background: var(--rbm-filter-panel-option-active-background);
            font-weight: var(--rbm-filter-panel-option-active-font-weight);
            color: var(--rbm-filter-panel-option-active-color);
        }
        &-equal {
            // 7.5px为间距，默认3列
            width: calc((100% - 15px) / 3);
        }
        &-hidden {
            display: none;
        }
        &-disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        &-ellipsis {
            display: block;
            text-align: center;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; //溢出不换行
        }
    }

    &-operation {
        display: flex;
        align-items: center;
        height: 64px;
        & > .rbm-button {
            width: 50%;
            height: 40px;

            font-size: 14px;
            color: var(--rbm-font-color-text);
            font-weight: 500;
            &:last-child {
                margin-left: 8px;
            }
        }
    }
}