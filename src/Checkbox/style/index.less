@import '../../var.less';

@prefixCls: ~'rbm-checkbox';
@checkboxIconCls: ~'@{prefixCls}-icon';

:root {
    --rbm-checkbox-margin-left: unset;
    --_rbm-checkbox-margin-left: 6px;

    --rbm-checkbox-margin-right: unset;
    --_rbm-checkbox-margin-right: 6px;

    --rbm-checkbox-size: unset;
    --_rbm-checkbox-size: 16px;

    --rbm-checkbox-icon-width: unset;
    --_rbm-checkbox-icon-width: 1px;

    --rbm-checkbox-icon-border-radius: unset;
    --_rbm-checkbox-icon-border-radius: 3px;

    --rbm-checkbox-icon-checked-disabled-color: unset;
    --_rbm-checkbox-icon-checked-disabled-color: #fff19a;
}

.input-after {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    position: absolute;
    left: 5px;
    top: 2px;
    display: table;
    width: 4px;
    height: 8px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    opacity: 0;
    content: ' ';
}
.@{prefixCls} {
    &-wrapper {
        -webkit-tap-highlight-color: transparent;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
    }

    &-label {
        margin-left: var(--rbm-checkbox-margin-left, var(--_rbm-checkbox-margin-left));
        &-left {
            margin-right: var(--rbm-checkbox-margin-right, var(--_rbm-checkbox-margin-right));
        }
    }
    &-icon {
        position: relative;
        top: 0;
        left: 0;
        display: inline-block;
        width: var(--rbm-checkbox-size, var(--_rbm-checkbox-size));
        height: var(--rbm-checkbox-size, var(--_rbm-checkbox-size));
        box-sizing: border-box;
        border-width: var(--rbm-checkbox-icon-width, var(--_rbm-checkbox-icon-width));
        border-style: solid;
        border-radius: var(--rbm-checkbox-icon-border-radius, var(--_rbm-checkbox-icon-border-radius));
        border-color: var(--rbm-font-color-desc);
        background-color: var(--rbm-gray-6);
        & > svg {
            display: block;
            width: 100%;
            height: 100%;
        }
    }
    & > input {
        width: var(--rbm-checkbox-size, var(--_rbm-checkbox-size));
        height: var(--rbm-checkbox-size, var(--_rbm-checkbox-size));
        border-radius: 50%;
        position: absolute;
        left: 0;
        z-index: 9999;
        cursor: pointer;
        opacity: 0;
        top: 0;
        bottom: 0;
        right: 0;
    }
    display: inline-flex;
    position: relative;
    vertical-align: middle;
    line-height: 1;

    // *****选中****
    &-checked {
        .@{checkboxIconCls} {
            background: var(--rbm-color-primary);
            border-color: var(--rbm-color-primary);
        }
    }

    // *****置灰下****
    &-disabled {
        // opacity: 0.6;
        cursor: default;
        .@{checkboxIconCls} {
            background: var(--rbm-gray-5);
            border-color: var(--rbm-font-color-comm);
        }
    }

    // 置灰且选中下背景色调整
    &-checked&-disabled > .@{checkboxIconCls} {
        background: var(--rbm-checkbox-icon-checked-disabled-color, var(--_rbm-checkbox-icon-checked-disabled-color));
        border-color: transparent;
    }

    // *****半勾选****
    &-indeterminate {
        .@{checkboxIconCls} {
            background: var(--rbm-color-primary);
            border-color: var(--rbm-color-primary);
        }
    }

    &-rtl {
        direction: rtl;
    }
    &-rtl > &-label {
        margin-left: 0px;
        margin-right: 6px;
        &-left {
            margin-right: 0px;
            margin-left: 6px;
        }
    }
}
