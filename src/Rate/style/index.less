@import '../../var.less';

// 定义类名前缀
@class-prefix-rate: ~'rbm-rate';

:root {
    --rbm-rate-star-color: unset;
    --_rbm-rate-star-color: var(--rbm-gray-4);

    --rbm-rate-star-active-color: unset;
    --_rbm-rate-star-active-color: #ffdd19;

    --rbm-rate-star-size: unset;
    --_rbm-rate-star-size: 16.8px;

    --rbm-rate-font-size: unset;
    --_rbm-rate-font-size: 14px;
}

.@{class-prefix-rate} {
    display: inline-flex;
    touch-action: pan-y;
    user-select: none;

    &-box {
        position: relative;
    }

    &-star-icon {
        font-size: var(--rbm-rate-star-size, var(--_rbm-rate-star-size));
    }

    &-star {
        text-align: center;
        overflow: hidden;
        cursor: pointer;
        box-sizing: border-box;
        transition: all 0.3s;
        color: var(--rbm-rate-star-color, var(--_rbm-rate-star-color));
        font-size: var(--rbm-rate-font-size, var(--_rbm-rate-font-size));

        &-half {
            padding-right: 0;
            width: 50%;
            position: absolute;
            left: 0;
            top: 0;
        }

        &-active {
            color: var(--rbm-rate-star-active-color, var(--_rbm-rate-star-active-color));
        }

        &-readonly {
            cursor: unset;
        }
    }

    &-rtl {
        direction: rtl;
        // transform: rotate(180deg);
        &-half {
            left: 9px;
        }
    }

    &-rtl > &-box {
        // transform: rotate(180deg);
    }
}
