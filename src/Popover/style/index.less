@class-prefix-popover: ~'rbm-popover';

:root {
    --rbm-popover-background: unset;
    --_rbm-popover-background: rgba(34, 34, 34, 0.8);
    
    --rbm-popover-light-background: unset;
    --_rbm-popover-light-background: #fff;
    
    --rbm-popover-text-color: unset;
    --_rbm-popover-text-color: #fff;
    
    --rbm-popover-light-text-color: unset;
    --_rbm-popover-light-text-color: #222;
    
    --rbm-popover-border-radius: unset;
    --_rbm-popover-border-radius: 6px;
    
    --rbm-popover-font-size: unset;
    --_rbm-popover-font-size: 14px;
    
    --rbm-popover-arrow-background: unset;
    --_rbm-popover-arrow-background: rgba(34, 34, 34, 0.8);
    
    --rbm-popover-light-arrow-background: unset;
    --_rbm-popover-light-arrow-background: #fff;
    
    --rbm-popover-active-background: unset;
    --_rbm-popover-active-background: #404040;
    
    --rbm-popover-light-active-background: unset;
    --_rbm-popover-light-active-background: #f5f6fa;
    
    --rbm-popover-disabled-color: unset;
    --_rbm-popover-disabled-color: var(--rbm-gray-1);
    
    --rbm-popover-light-disabled-color: unset;
    --_rbm-popover-light-disabled-color: var(--rbm-gray-2);
    
    --rbm-popover-border-color: unset;
    --_rbm-popover-border-color: #858687;
    
    --rbm-popover-light-border-color: unset;
    --_rbm-popover-light-border-color: var(--rbm-gray-4);
    
    --rbm-popover-shadow: unset;
    --_rbm-popover-shadow: var(--rbm-shadow-medium);
    
    --rbm-popover-item-height: unset;
    --_rbm-popover-item-height: 44px;
    
    --rbm-popover-item-padding: unset;
    --_rbm-popover-item-padding: 0 12px;
    
    --rbm-popover-icon-size: unset;
    --_rbm-popover-icon-size: 16px;
}

.@{class-prefix-popover} {
    &-popup-wrap {
        position: absolute;
        width: max-content;
        left: 0;
        top: 0;
    }

    &-popup-arrow {
        position: absolute;
        width: 10px;
        height: 5px;
        // border-style: solid;
        overflow: hidden;

        &::after {
            content: '';
            position: absolute;
            width: 7px;
            height: 7px;
            bottom: 0;
            inset-inline: 0;
            margin: auto;
            border-radius: 0;
            transform: translateY(50%) rotate(-135deg);
            // box-shadow: 0px 8px 24px 0px #0000001E,0px 0px 2px 0px #00000028;

            z-index: 0;
            background: var(--rbm-popover-arrow-background, var(--_rbm-popover-arrow-background));
        }
    }

    &-popup-inner {
        background-color: var(--rbm-popover-background, var(--_rbm-popover-background));
        font-size: var(--rbm-popover-font-size, var(--_rbm-popover-font-size));
        color: var(--rbm-popover-text-color, var(--_rbm-popover-text-color));
        border-radius: var(--rbm-popover-border-radius, var(--_rbm-popover-border-radius));
        overflow: hidden;
        font-family: var(--rbm-font-family);
    }

    &-popup-menu-item {
        user-select: none;

        &:active {
            background: var(--rbm-popover-active-background, var(--_rbm-popover-active-background));
        }
    }

    &-popup-menu-item-disabled {
        color: var(--rbm-popover-disabled-color, var(--_rbm-popover-disabled-color));

        &:active {
            background: none;
        }
    }

    &-popup-menu-item:last-child &-popup-menu-item-content {
        border-bottom: none;
    }

    &-popup-menu-item-content {
        height: var(--rbm-popover-item-height, var(--_rbm-popover-item-height));
        display: flex;
        flex-direction: row;
        align-items: center;
        margin: var(--rbm-popover-item-padding, var(--_rbm-popover-item-padding));
        border-bottom: 1px solid var(--rbm-popover-border-color, var(--_rbm-popover-border-color));
        max-width: 70vw;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    &-popup-wrap-light {
        .@{class-prefix-popover} {
            &-popup-arrow {
                border-color: transparent transparent #fff transparent;

                &::after {
                    content: '';
                    position: absolute;
                    width: 7px;
                    height: 7px;
                    bottom: 0;
                    inset-inline: 0;
                    margin: auto;
                    border-radius: 0;
                    transform: translateY(50%) rotate(-135deg);
                    box-shadow: var(--rbm-popover-shadow, var(--_rbm-popover-shadow));
                    z-index: 0;
                    background: var(--rbm-popover-light-arrow-background, var(--_rbm-popover-light-arrow-background));
                }
            }

            &-popup-inner {
                background-color: var(--rbm-popover-light-background, var(--_rbm-popover-light-background));
                box-shadow: var(--rbm-popover-shadow, var(--_rbm-popover-shadow));
            }

            &-popup-menu-item {
                color: var(--rbm-popover-light-text-color, var(--_rbm-popover-light-text-color));
            }

            &-popup-menu-item-content {
                border-bottom: 1px solid var(--rbm-popover-light-border-color, var(--_rbm-popover-light-border-color));
            }

            &-popup-menu-item {
                &:active {
                    background: var(--rbm-popover-light-active-background, var(--_rbm-popover-light-active-background));
                }
            }

            &-popup-menu-item-disabled {
                color: var(--rbm-popover-light-disabled-color, var(--_rbm-popover-light-disabled-color));
            }

            &-popup-menu-item-disabled {
                &:active {
                    background: none;
                }
            }
        }
    }

    &-icon-size {
        font-size: var(--rbm-popover-icon-size, var(--_rbm-popover-icon-size));
    }
}
