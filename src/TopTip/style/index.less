@class-prefix-toptip: ~'rbm-toptip';

:root {
    --rbm-toptip-font-size: var(--rbm-font-size-s);
    --rbm-toptip-line-height: var(--rbm-line-height-xs);
    --rbm-toptip-padding-v: var(--rbm-space-4);
    --rbm-toptip-padding-h: var(--rbm-space-6);
    --rbm-toptip-bg-color: unset;
    --_rbm-toptip-bg-color: white;
    --rbm-toptip-text-color: unset;
    --_rbm-toptip-text-color: var(--rbm-font-color-text);
}

.@{class-prefix-toptip} {
    background-color: var(--rbm-toptip-bg-color, var(--_rbm-toptip-bg-color));
    color: var(--rbm-toptip-text-color, var(--_rbm-toptip-text-color));
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    font-size: var(--rbm-toptip-font-size);
    line-height: var(--rbm-toptip-line-height);
    padding: var(--rbm-toptip-padding-v) var(--rbm-toptip-padding-h);
    direction: ltr;
    i {
        color: var(--rbm-toptip-text-color, var(--_rbm-toptip-text-color)) !important;
    }
    &-rtl {
        direction: rtl;
        text-align: right;
        .@{class-prefix-toptip}-icon {
            margin-right: 0px;
            margin-left: var(--rbm-space-4);
        }
        .@{class-prefix-toptip}-control {
            & > *:nth-child(1) {
                margin-left: 0px;
                margin-right: var(--rbm-space-4);
            }
            & > *:nth-child(2) {
                margin-left: 0px;
                margin-right: var(--rbm-space-5);
            }
        }
    }
    &-info {
        --_rbm-toptip-bg-color: #fff8e1;
    }
    &-warning {
        --_rbm-toptip-bg-color: #fff1e7;
    }
    &-card {
        --_rbm-toptip-bg-color: var(--rbm-page-bg);
    }
    &-success {
        --_rbm-toptip-bg-color: rgba(0, 191, 127, 0.1);
    }
    &-danger {
        --_rbm-toptip-bg-color: #fff2f3;
    }
    &-icon {
        flex: none;
        margin-right: var(--rbm-space-4);
        font-size: var(--rbm-font-size-md);
    }
    &-icon-size {
        font-size: var(--rbm-font-size-l);
    }
    &-content-box {
        margin: 0px;
        flex: 1;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &-control {
        flex: none;
        height: var(--rbm-toptip-line-height);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: row;
        & > *:nth-child(1) {
            margin-left: var(--rbm-space-4);
        }
        & > *:nth-child(2) {
            margin-left: var(--rbm-space-5);
        }
        &-icon {
            display: inline-block;
            height: var(--rbm-toptip-line-height);
        }
    }
}
