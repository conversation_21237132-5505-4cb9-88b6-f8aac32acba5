// 定义类名前缀
@class-prefix-List: ~'rbm-List';
@class-prefix-ListItem: ~'rbm-ListItem';

:root {
    --rbm-list-padding: var(--rbm-space-5);

    --rbm-list-item-margin-bottom: var(--rbm-space-5);
    --rbm-list-item-border-radius: 10.5px;
    --rbm-list-item-padding: var(--rbm-space-7) var(--rbm-space-6);
    --rbm-list-item-background: var(--rbm-gray-6);

    --rbm-list-item-title-font-size: var(--rbm-font-size-md);
    --rbm-list-item-title-font-weight: var(--rbm-font-weight-normal);
    --rbm-list-item-title-color: var(--rbm-font-color-text);

    --rbm-list-item-sub-title-font-size: var(--rbm-font-size-s);
    --rbm-list-item-sub-title-font-weight: var(--rbm-font-weight-normal);
    --rbm-list-item-sub-title-color: var(--rbm-font-color-sub);
    --rbm-list-item-sub-title-margin-top: var(--rbm-space-4);

    --rbm-list-item-disabled-color: var(--rbm-font-color-desc);

    --rbm-list-item-arrow-icon-font-size: var(--rbm-font-size-l);
    --rbm-list-item-arrow-icon-margin-left: calc(var(--rbm-space-5) / 2);

    --rbm-list-item-avatar-margin-right: var(--rbm-space-6);

    --rbm-list-item-extra-margin-left: var(--rbm-space-5);
}

// 使用带前缀的类名变量
.@{class-prefix-List} {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: var(--rbm-list-padding);
    box-sizing: border-box;

    &-header {
        flex-shrink: unset;
    }

    &-content {
        flex: 1;
        overflow: hidden;
        // margin-top: 10px;

        &-scroll {
            overflow: scroll;
            height: 100%;

            scrollbar-width: none;

            /* 对于Firefox隐藏滚动条 */
            /* 对于Webkit浏览器（如Chrome、Safari）隐藏滚动条 */
            &::-webkit-scrollbar {
                display: none;
            }

            /* 对于IE和Edge隐藏滚动条 */
            -ms-overflow-style: none;
        }
    }

    .@{class-prefix-ListItem} {
        margin-bottom: var(--rbm-list-item-margin-bottom);
    }
}

.@{class-prefix-ListItem} {
    display: flex;
    border-radius: var(--rbm-list-item-border-radius);
    padding: var(--rbm-list-item-padding);
    background: var(--rbm-list-item-background);
    align-items: center;

    &-disabled {
        .@{class-prefix-ListItem}-avatar,
        .@{class-prefix-ListItem}-content-title :nth-child(1),
        .@{class-prefix-ListItem}-content-sub-title,
        .@{class-prefix-ListItem}-extra {
            color: var(--rbm-list-item-disabled-color);
        }
    }

    &-avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--rbm-list-item-avatar-margin-right);
    }

    &-content {
        flex: 1 1 auto;
        min-width: 0;

        &-title {
            display: flex;
            overflow: hidden;
            flex-wrap: nowrap;
            justify-content: space-between;
            align-items: center;

            :nth-child(1) {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: var(--rbm-list-item-title-font-size);
                color: var(--rbm-list-item-title-color);
                font-weight: var(--rbm-list-item-title-font-weight);
            }
        }

        .arrow-icon {
            margin-left: var(--rbm-list-item-arrow-icon-margin-left);
            font-size: var(--rbm-list-item-arrow-icon-font-size);
        }

        &-sub-title {
            font-size: var(--rbm-list-item-sub-title-font-size);
            color: var(--rbm-list-item-sub-title-color);
            font-weight: var(--rbm-list-item-sub-title-font-weight);
            margin-top: var(--rbm-list-item-sub-title-margin-top);
        }
    }

    &-extra {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        margin-left: var(--rbm-list-item-extra-margin-left);
    }
}
