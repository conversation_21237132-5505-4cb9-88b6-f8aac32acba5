@form-prefix-cls: ~'rbm-form';
@form-item-prefix-cls: ~'@{form-prefix-cls}-item';

:root {
    --rbm-form-item-border-bottom: 0.5px solid #eeeeee;

    --rbm-form-item-label-width: 80px;

    --rbm-form-item-vertical-padding: 14px;
    --rbm-form-item-horizontal-padding: 12px;
}

.@{form-prefix-cls} {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    list-style: none;
    color: var(--rbm-font-color-text);
    font-size: var(--rbm-font-size-md);
    background-color: var(--rbm-gray-6);
    font-family: var(--rbm-font-family);

    input {
        font-weight: var(--rbm-font-weight-bold);
    }

    input::placeholder {
        font-weight: var(--rbm-font-weight-normal);
        color: var(--rbm-font-color-desc);
    }

    &-input-error-right{
        text-align: right;
    }

    &-input-error-center{
        text-align: center;
    }

    &-input-error-left{
        text-align: left;
    }

    .@{form-item-prefix-cls} {
        box-sizing: border-box;
        color: var(--rbm-font-color-text);
        font-size: var(--rbm-font-size-md);
        padding: var(--rbm-form-item-vertical-padding) var(--rbm-form-item-horizontal-padding) 0 var(--rbm-form-item-horizontal-padding);

        &-border-bottom {
            padding-bottom: var(--rbm-form-item-vertical-padding);
            border-bottom: var(--rbm-form-item-border-bottom);
        }

        &-border-none {
            border-bottom: none;
        }

        &-tooltip {
            margin-left: 4px;
        }

        &-row {
            display: flex;
            align-items: start;
        }

        &-explain {
            margin-top: 4px;
            font-size: var(--rbm-font-size-s);
            color: var(--rbm-font-color-desc);
        }

        &-label {
            display: inline-block;
            flex-grow: 0;
            overflow: hidden;
            white-space: nowrap;
            vertical-align: middle;
            width: var(--rbm-form-item-label-width);

            &-left {
                text-align: left;
            }

            &-right {
                text-align: right;
            }

            &-wrap {
                overflow: unset;
                white-space: unset;
            }

            > label {
                position: relative;
                display: inline-flex;
                align-items: center;
                max-width: 100%;
                color: var(--rbm-font-color-text);
                font-size: var(--rbm-font-size-md);
                overflow: hidden;

                &.@{form-item-prefix-cls}-required:not(
                        .@{form-item-prefix-cls}-required-mark-optional
                    )::before {
                    display: inline-block;
                    color: var(--rbm-color-danger);
                    font-size: var(--rbm-font-size-md);
                    content: '*';

                    .@{form-prefix-cls}-hide-required-mark & {
                        display: none;
                    }
                }

                .@{form-item-prefix-cls}-optional {
                    display: inline-block;
                    .@{form-prefix-cls}-hide-required-mark & {
                        display: none;
                    }
                }

                .@{form-item-prefix-cls}-tooltip {
                    cursor: help;
                    writing-mode: horizontal-tb;
                }

                &::after {
                    position: relative;
                    top: -0.5px;
                }
            }
        }
        &-label-left {
            text-align: left;
        }

        &-required::before {
            display: inline-block;
            margin-right: 2px;
            content: '*';
            vertical-align: middle;
            font-size: inherit;
            color: var(--rbm-color-danger);
        }

        &-explain-error {
            font-size: var(--rbm-font-size-s);
            color: var(--rbm-color-danger);
            margin-top: 4px;
        }

        &-inline {
            display: flex;
            flex-wrap: wrap;

            .@{form-item-prefix-cls} {
                flex: none;
                flex-wrap: nowrap;
                margin-right: 16px;
                margin-bottom: 0;
            }
        }

        &-hidden,
        &-hidden.@{form-item-prefix-cls}-row {
            display: none;
        }
        &-disabled {
            opacity: 0.5;
            cursor: not-allowed;
            input {
                pointer-events: none;
            }
        }
        &-description {
            margin-top: 8px;
            color: var(--rbm-font-color-desc);
            white-space: normal;
            word-wrap: break-word;
            word-break: break-all;
            font-size: var(--rbm-font-size-s);
        }
    }

    &-horizontal {
        .@{form-item-prefix-cls}-horizontal {
            flex: 1 1 0;
        }
    }

    &-vertical {
        .@{form-item-prefix-cls} {
            &-row {
                flex-direction: column;
            }
            
            &-row[layout="horizontal"] {
                width: inherit;
                flex-direction: row;
            }

            &-label {
                text-align: left;
                width: var(--rbm-form-item-label-width);
                padding-bottom: 4px;
                > label {
                    margin: 0;

                    &::after {
                        display: none;
                    }
                }
            }

            &-label[layout="horizontal"] {
               width: var(--rbm-form-item-label-width);
            }

            &-explain-error {
                margin-top: 0;
            }

            .@{form-item-prefix-cls}-control {
                width: 100%;
            }
        }
        .@{form-prefix-cls}-control:not[layout="horizontal"] {
            width: 100%;
            margin-left: 0;
        }
        .rbm-input {
            margin-left: 0;
        }
    }

    &-disabled {
        opacity: 0.5;
        cursor: not-allowed;
        input {
            pointer-events: none;
        }
    }

    &-input-extra-arrow-wrap {
        display: flex;
        align-items: center;
        // flex: 1;
        // height: 100%;
    }
    &-control {
        flex: 1;
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    &-control-input {
        flex: 1;
    }
    .has-error&-control-input-text-red input {
        color: var(--rbm-color-danger);
        caret-color: var(--rbm-font-color-text);
    }
    .rbm-input {
        padding: 0;
        height: auto;
    }
}

.@{form-prefix-cls}-transparent {
    background-color: transparent;
}