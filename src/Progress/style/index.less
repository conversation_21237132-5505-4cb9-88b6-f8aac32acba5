@import '../../var.less';

@class-prefix-progress: ~'rbm-progress';

:root {   
    --rbm-progress-text-color: var(--rbm-font-color-text);

    --rbm-progress-font-size: unset;
    --_rbm-progress-font-size: var(--rbm-font-size-md);

    --rbm-progress-background: unset;
    --_rbm-progress-background: var(--rbm-color-brand);

    --rbm-progress-inner-background: var(--rbm-page-bg);

    --rbm-progress-animation-background: #222222;

    --rbm-progress-line-border-radius: 100px;

    --rbm-progress-line-text-margin-left: 10px;

    --rbm-progress-line-text-min-width: 40px;

    --rbm-progress-circle-text-padding: 0 4px;

    --rbm-progress-circle-line-height: 1.4;
}

.@{class-prefix-progress} {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--rbm-progress-text-color);
    font-size: var(--rbm-progress-font-size, var(--_rbm-progress-font-size));
    font-family: var(--rbm-font-family);

    &-outer {
        display: inline-flex;
        align-items: center;
        width: 100%;
    }

    &-bg {
        overflow: hidden;
        position: relative;
        background: var(--rbm-progress-background, var(--_rbm-progress-background));
        border-radius: var(--rbm-progress-line-border-radius);
        transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
    }

    &-inner {
        position: relative;
        display: inline-block;
        width: 100%;
        flex: 1;
        overflow: hidden;
        vertical-align: middle;
        background-color: var(--rbm-progress-inner-background);
        border-radius: var(--rbm-progress-line-border-radius);
    }

    &-status-icon {
        font-size: var(--rbm-progress-font-size, var(--_rbm-progress-font-size));
        position: relative;
        top: 2px;
    }

    &-status-exception {
        .@{class-prefix-progress}-bg {
            --_rbm-progress-background: var(--rbm-color-danger);
        }
    }
    &-status-success {
        .@{class-prefix-progress}-bg {
            --_rbm-progress-background: var(--rbm-color-success);
        }
    }
    &-status-active {
        .@{class-prefix-progress}-bg {
            &::before {
                position: absolute;
                inset: 0;
                background-color: var(--rbm-progress-animation-background);
                border-radius: var(--rbm-progress-line-border-radius);
                opacity: 0;
                animation-duration: 2.4s;
                animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
                animation-iteration-count: infinite;
                content: '';
                animation-name: active; // 默认动画
            }
        }
    }

    &-line {
        position: relative;
        --_rbm-progress-font-size: var(--rbm-font-size-md);

        .@{class-prefix-progress}-text {
            margin-left: var(--rbm-progress-line-text-margin-left);
            min-width: var(--rbm-progress-line-text-min-width);
        }
    }

    &-small {    
        --_rbm-progress-font-size: var(--rbm-font-size-s);
    
        .@{class-prefix-progress}-status-icon {
            --_rbm-progress-font-size: var(--rbm-font-size-s);
        }
    }

    &-circle {
        position: relative;
        display: inline-block;
        text-align: center;

        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        &-trail {
            stroke: var(--rbm-progress-inner-background);
        }
        &-hover {
            fill: none;
            stroke: var(--rbm-progress-background, var(--_rbm-progress-background));
            stroke-linecap: round;
        }
        .@{class-prefix-progress}-text {
            position: absolute;
            top: 50%;
            left: 0;
            box-sizing: border-box;
            width: 100%;
            padding: var(--rbm-progress-circle-text-padding);
            color: var(--rbm-progress-text-color);
            font-size: 1em;
            line-height: var(--rbm-progress-circle-line-height);
            transform: translateY(-50%);
        }
    }

    /** RTL **/
    &-line.@{class-prefix-progress}-rtl {
        .@{class-prefix-progress}-text {
            margin-left: 0; // RTL情况下的 margin-left
            margin-right: var(--rbm-progress-line-text-margin-left);
        }
    }
    &-status-active.@{class-prefix-progress}-rtl {
        .@{class-prefix-progress}-bg::before {
            animation-name: active-reverse !important; // 特殊情况
        }
    }

    @keyframes active {
        0% {
            transform: translateX(-100%) scaleX(0);
            opacity: 0.1;
        }
        20% {
            transform: translateX(-100%) scaleX(0);
            opacity: 0.5;
        }
        to {
            transform: translateX(0) scaleX(1);
            opacity: 0;
        }
    }

    @keyframes active-reverse {
        0% {
            transform: translateX(100%) scaleX(0);
            opacity: 0.1;
        }
        20% {
            transform: translateX(100%) scaleX(0);
            opacity: 0.5;
        }
        to {
            transform: translateX(0) scaleX(1);
            opacity: 0;
        }
    }
}
