import React from 'react';
import useMemo from 'rc-util/lib/hooks/useMemo';
import { merge } from 'rc-util/lib/utils/set';
import { Locale } from '../locale/interface';
import LocaleProvider from '../locale/provider';
import defaultLocale from '../locale/lang/zh-CN';
import ValidateMessagesContext from '../Form/validateMessagesContext';
import { kebabCase } from '../utils/unit';

export enum directionEnum {
    'RTL' = 'RTL',
    'rtl' = 'RTL',
    'LTR' = 'LTR',
    'ltr' = 'LTR',
}
export type DirectionType = 'RTL' | 'LTR' | 'rtl' | 'ltr';
const defaultPrefixCls = 'rbm';

export const DirectionContext = React.createContext<DirectionType>('LTR');
export const PrefixClsContext = React.createContext<string>(defaultPrefixCls);

export type ConfigType = {
    direction?: DirectionType;
    prefixCls?: string;
    locale?: Locale;
    themeVars?: Record<string, string | number>;
    tag?: React.ElementType;
    style?: React.CSSProperties;
    className?: string;
};

type holderRenderType = (children: React.ReactNode) => React.ReactNode;

type GlobalConfigProps = {
    prefixCls?: string;
    direction?: DirectionType;
    locale?: Locale;
    holderRender?: holderRenderType;
};

export const GlobalConfigContext = React.createContext<ConfigType>({
    direction: 'LTR',
    prefixCls: defaultPrefixCls,
});
export interface ConfigContextProps {
    config?: ConfigType;
    children?: React.ReactNode;
}

let globalPrefixCls: string;
let globalDirection: DirectionType;
let globalLocale: Locale;
let globalHolderRender: holderRenderType | undefined;

const setGlobalConfig = (props: GlobalConfigProps) => {
    const { prefixCls, direction, locale, holderRender } = props;
    if (prefixCls !== undefined) {
        globalPrefixCls = prefixCls;
    }
    if (locale !== undefined) {
        globalLocale = locale;
    }
    if (direction !== undefined) {
        globalDirection = direction;
    }
    if (holderRender) {
        globalHolderRender = holderRender;
    }
};

function getGlobalPrefixCls() {
    return globalPrefixCls || defaultPrefixCls;
}

function mapThemeVarsToCSSVars(themeVars: Record<string, string | number>, prefix: string) {
    themeVars = themeVars || {};
    const cssVars: Record<string, string | number> = {};
    Object.keys(themeVars).forEach(key => {
        if (key.toString().startsWith(`--${prefix}-`)) {
            cssVars[key] = themeVars[key];
        } else {
            cssVars[`--${prefix}-${kebabCase(key)}`] = themeVars[key];
        }
    });
    return cssVars;
}

export const globalConfig = () => ({
    getPrefixCls: (suffixCls?: string, customizePrefixCls?: string) => {
        if (customizePrefixCls) {
            return customizePrefixCls;
        }
        return suffixCls ? `${getGlobalPrefixCls()}-${suffixCls}` : getGlobalPrefixCls();
    },
    getDirection: () => globalDirection || 'LTR',
    getLocale: () => globalLocale || defaultLocale,
    holderRender: globalHolderRender,
});

type ConfigProviderType = React.FC<ConfigContextProps> & {
    GlobalConfigContext: typeof GlobalConfigContext;
    config: typeof setGlobalConfig;
};

export const ConfigProvider: ConfigProviderType = ({ config, children }) => {
    const originDirection = React.useContext(DirectionContext);
    const originPrefixCls = React.useContext(PrefixClsContext);

    const memoedConfig = useMemo(
        () => config,
        config,
        (prevConfig = {}, currentConfig = {}) => {
            const prevKeys = Object.keys(prevConfig) as Array<keyof typeof config>;
            const currentKeys = Object.keys(currentConfig) as Array<keyof typeof config>;
            return (
                prevKeys.length !== currentKeys.length ||
                prevKeys.some(key => prevConfig[key] !== currentConfig[key])
            );
        },
    );

    const TagElement = memoedConfig?.tag;

    const directionValue = directionEnum[memoedConfig?.direction as DirectionType];

    const validateMessages = React.useMemo(
        () =>
            merge(
                defaultLocale.Form.defaultValidateMessages,
                memoedConfig?.locale?.Form?.defaultValidateMessages || {},
            ),
        [memoedConfig],
    );
    const prefixClsValue = memoedConfig?.prefixCls ?? defaultPrefixCls;

    const configStyle = React.useMemo<React.CSSProperties | undefined>(() => {
        let resStyle = {};

        if (config.themeVars) {
            resStyle = { ...mapThemeVarsToCSSVars(config?.themeVars, 'rbm') };
        }
        if (config.style) {
            resStyle = { ...config.style };
        }
        return resStyle;
    }, [config.style, config.themeVars]);

    const directionOnChange = React.useCallback(() => {
        if (config?.direction === 'RTL' || config?.direction === 'rtl') {
            document.documentElement.setAttribute('dir', 'rtl');
            document.documentElement.setAttribute('class', `${prefixClsValue}-rtl`);
        } else {
            document.documentElement.setAttribute('dir', 'ltr');
            document.documentElement.setAttribute('class', '');
        }
    }, [config?.direction]);

    React.useEffect(() => {
        directionOnChange();
    }, [config?.direction]);

    let childNode = children;

    // istanbul ignore next
    if (Object.keys(validateMessages).length > 0) {
        childNode = (
            <ValidateMessagesContext.Provider value={validateMessages}>
                {childNode}
            </ValidateMessagesContext.Provider>
        );
    }

    if (memoedConfig?.locale) {
        childNode = <LocaleProvider locale={memoedConfig?.locale}>{childNode}</LocaleProvider>;
    }

    const propValue = React.useMemo(
        () => ({
            direction: directionValue || originDirection,
            prefixCls: memoedConfig?.prefixCls || originPrefixCls,
            locale: memoedConfig?.locale,
        }),
        [
            directionValue,
            originDirection,
            memoedConfig?.prefixCls,
            originPrefixCls,
            memoedConfig?.locale,
        ],
    );
    const renderContent = () => {
        if (TagElement) {
            return (
                <TagElement style={configStyle} className={memoedConfig.className}>
                    {childNode}
                </TagElement>
            );
        }
        return childNode;
    };

    return (
        <GlobalConfigContext.Provider value={propValue}>
            {renderContent()}
        </GlobalConfigContext.Provider>
    );
};

ConfigProvider.GlobalConfigContext = GlobalConfigContext;
ConfigProvider.config = setGlobalConfig;

export default ConfigProvider;
