@class-prefix-navigationBar: ~'rbm-NavigationBar';

:root {
    --rbm-navigationBar-padding-right: unset;
    --_rbm-navigationBar-padding-right: 12px;

    --rbm-navigationBar-padding-left: unset;
    --_rbm-navigationBar-padding-left: 6px;

    --rbm-navigationBar-back-padding: unset;
    --_rbm-navigationBar-back-padding: 6px;

    --rbm-navigationBar-back-max-width: unset;
    --_rbm-navigationBar-back-max-width: 30%;

    --rbm-navigationBar-title-padding: unset;
    --_rbm-navigationBar-title-padding: 0 12px;

    --rbm-navigationBar-action-max-width: unset;
    --_rbm-navigationBar-action-max-width: 30%;

    --rbm-navigationBar-padding-right-rtl: unset;
    --_rbm-navigationBar-padding-right-rtl: 6px;

    --rbm-navigationBar-padding-left-rtl: unset;
    --_rbm-navigationBar-padding-left-rtl: 12px;
}

.@{class-prefix-navigationBar} {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    padding-right: var(--rbm-navigationBar-padding-right, var(--_rbm-navigationBar-padding-right));
    padding-left: var(--rbm-navigationBar-padding-left, var(--_rbm-navigationBar-padding-left));
    white-space: nowrap;
    font-size: var(--rbm-font-size-md);
    color: var(--rbm-font-color-text);
    line-height: var(--rbm-line-height-s);
    font-weight: var(--rbm-font-weight-normal);
    &-back {
        padding: var(--rbm-navigationBar-back-padding, var(--_rbm-navigationBar-back-padding));
        display: flex;
        align-items: center;
        flex: 1;
        max-width: var(--rbm-navigationBar-back-max-width, var(--_rbm-navigationBar-back-max-width));
        overflow: hidden;
    }
    &-title {
        flex: auto;
        text-align: center;
        font-size: var(--rbm-font-size-l);
        line-height: var(--rbm-line-height-xl);
        font-weight: var(--rbm-font-weight-bold);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: var(--rbm-navigationBar-title-padding, var(--_rbm-navigationBar-title-padding));
    }
    &-action {
        flex: 1;
        max-width: var(--rbm-navigationBar-action-max-width, var(--_rbm-navigationBar-action-max-width));
        overflow: hidden;
        text-align: right;
    }
    &-top_fix {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
    }
    &-safe_area_top {
        padding-top: constant(safe-area-inset-top);
        padding-top: env(safe-area-inset-top);
    }
    &-icon-size {
        font-size: var(--rbm-line-height-md);
    }
    &-rtl {
        direction: rtl;
        padding-left: var(--rbm-navigationBar-padding-left-rtl, var(--_rbm-navigationBar-padding-left-rtl));
        padding-right: var(--rbm-navigationBar-padding-right-rtl, var(--_rbm-navigationBar-padding-right-rtl));
        .@{class-prefix-navigationBar}-action {
            text-align: left;
        }
        .@{class-prefix-navigationBar}-icon-size {
            transform: rotate(180deg) !important;
        }
    }
    &-immersion {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 0;
    }
}
