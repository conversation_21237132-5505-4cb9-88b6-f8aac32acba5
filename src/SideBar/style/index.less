@class-prefix-sidebar: ~'rbm-sidebar';

:root {
    --rbm-sidebar-width: 80px;
    --rbm-sidebar-sub-width: 88px;
    --rbm-sidebar-font-size: var(--rbm-font-size-md);
    --rbm-sidebar-color: var(--rbm-font-color-sub);
    
    --rbm-sidebar-item-padding: var(--rbm-space-6);
    --rbm-sidebar-item-background: var(--rbm-page-bg);
    --rbm-sidebar-item-font-weight: var(--rbm-font-weight-normal);

    --rbm-sidebar-sub-item-padding-left: 19px;
    --rbm-sidebar-sub-item-padding-right: 13px;
    --rbm-sidebar-sub-item-background: var(--rbm-gray-6);
    --rbm-sidebar-sub-item-font-weight: var(--rbm-font-weight-normal);

    --rbm-sidebar-item-active-font-weight: var(--rbm-font-weight-bold);
    --rbm-sidebar-item-active-color: var(--rbm-font-color-text);
    --rbm-sidebar-item-active-background: var(--rbm-gray-6); 

    --rbm-sidebar-item-disabled-color: var(--rbm-font-color-comm);
    
    --rbm-sidebar-item-expend-background: var(--rbm-gray-6);
    
    --rbm-sidebar-border-radius: calc(var(--rbm-radius-2) * 2);
}

.@{class-prefix-sidebar} {
  font-size: var(--rbm-sidebar-font-size);
  width: var(--rbm-sidebar-width);
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  color: var(--rbm-sidebar-color);

  scrollbar-width: none;
  /* 对于Firefox隐藏滚动条 */
  /* 对于Webkit浏览器（如Chrome、Safari）隐藏滚动条 */
  &::-webkit-scrollbar {
      display: none;
  }
  /* 对于IE和Edge隐藏滚动条 */
  -ms-overflow-style: none;

  &-has-sub{
    width: var(--rbm-sidebar-sub-width);
  }

  &-item {
    font-weight: var(--rbm-sidebar-item-font-weight);
    display: flex;
    justify-content: space-between;
    align-items: center;
    word-break: break-all;
    padding: var(--rbm-sidebar-item-padding);
    background-color: var(--rbm-sidebar-item-background);
    -webkit-tap-highlight-color: transparent;
  }

  &-sub-item {
    padding-left: var(--rbm-sidebar-sub-item-padding-left);
    padding-right: var(--rbm-sidebar-sub-item-padding-right);
    font-weight: var(--rbm-sidebar-sub-item-font-weight);
    background-color: var(--rbm-sidebar-sub-item-background);
  }


  &-item-active {
    font-weight: var(--rbm-sidebar-item-active-font-weight);
    background-color: var(--rbm-sidebar-item-active-background);
    color: var(--rbm-sidebar-item-active-color);
  }

  &-item-expend {
    background-color: var(--rbm-sidebar-item-expend-background);
  }

  &-item-disabled {
    color: var(--rbm-sidebar-item-disabled-color);
    cursor: not-allowed;
  }

  &-bottom-right-radius{
    border-bottom-right-radius: var(--rbm-sidebar-border-radius);
  }

  &-top-right-radius{
    border-top-right-radius: var(--rbm-sidebar-border-radius);
  }

  &-extra-space{
    overflow: hidden;
    flex: auto;
    background-color: var(--rbm-sidebar-item-background);
  }
}