@class-prefix-segmented: ~'rbm-segmented';

:root {
    --rbm-segmented-background: #f1f2f6;
    --rbm-segmented-selected-background: var(--rbm-gray-6);

    --rbm-segmented-text-color: var(--rbm-font-color-text);
    --rbm-segmented-disabled-text-color: var(--rbm-gray-1);
    --rbm-segmented-selected-text-color: var(--rbm-font-color-text);

    --rbm-segmented-selected-shadow: 0px 4px 12px 0px #00000014;


    --rbm-segmented-border-radius: unset;
    --_rbm-segmented-border-radius: var(--rbm-radius-2);

    --rbm-segmented-item-border-radius: unset;
    --_rbm-segmented-item-border-radius: var(--rbm-radius-2);
}

.@{class-prefix-segmented} {
    font-weight: 400;
    display: inline-block;
    box-sizing: border-box;
    font-family: var(--rbm-font-family);
    color: var(--rbm-segmented-text-color);
    background: var(--rbm-segmented-background);
    border-radius: var(--rbm-segmented-border-radius, var(--_rbm-segmented-border-radius));

    &-group {
        position: relative;
        display: flex;
        align-items: stretch;
        justify-content: center;
        width: 100%;
    }

    &-large {
        --_rbm-segmented-border-radius: 6px;
        padding: 2px;
        .@{class-prefix-segmented}-item {
            --_rbm-segmented-item-border-radius: var(--rbm-radius-2);

            &-label {
                min-height: 20px;
                line-height: var(--rbm-line-height-md);
                padding: 4px 20px;
                font-size: var(--rbm-font-size-md);
            }
        }
    }

    &-small {
        --_rbm-segmented-border-radius: var(--rbm-radius-2);
        padding: 2px;
        .@{class-prefix-segmented}-item {
            --_rbm-segmented-item-border-radius: var(--rbm-radius-2);

            &-label {
                min-height: 14px;
                line-height: var(--rbm-line-height-xxs);
                padding: 3px 12px;
                font-size: var(--rbm-font-size-xxs);
            }
        }
    }

    &-block {
        display: flex;

        .@{class-prefix-segmented}-item {
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    &-item {
        position: relative;
        text-align: center;
        cursor: pointer;
        border-radius: var(--rbm-segmented-item-border-radius, var(--_rbm-segmented-item-border-radius));

        &-input {
            position: absolute;
            inset-block-start: 0;
            inset-inline-start: 0;
            width: 0;
            height: 0;
            opacity: 0;
            pointer-events: none;
        }

        &-label {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        &-icon {
            display: inline-flex;
            align-items: center;
            vertical-align: middle;
        }

        &:active {
            font-weight: 500;
            background: var(--rbm-segmented-background);
        }

        &-selected {
            font-weight: 500;
            background: var(--rbm-segmented-selected-background);
            box-shadow: var(--rbm-segmented-selected-shadow);
            color: var(--rbm-segmented-selected-text-color);
        }

        &-disabled {
            cursor: auto;
            color: var(--rbm-segmented-disabled-text-color);

            &:active {
                background: var(--rbm-segmented-background);
                font-weight: 400;
            }
        }
    }

    &-disabled {
        cursor: auto;
        color: var(--rbm-segmented-disabled-text-color);

        &:active {
            background: var(--rbm-segmented-background);
            font-weight: 400;
        }
    }
}