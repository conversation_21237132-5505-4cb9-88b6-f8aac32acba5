import React, { RefObject } from 'react';
import { useSpring, animated } from '@react-spring/web';
import { useDrag } from '@use-gesture/react';
import classNames from 'classnames';
import { SwipeActionProps, Action, SwipeActionRef, SideType, ColorType } from './interface';
import { mergeProps } from '../utils/with-default-props';
import { nearest } from '../utils/nearest';
import { PropagationEvent, withStopPropagation } from '../utils/with-stop-propagation';

const { forwardRef, useRef, useImperativeHandle, useEffect } = React;

const classPrefix = 'rbm-swipe-action';
const defaultProps: SwipeActionProps = {
    rightActions: [] as Action[],
    leftActions: [] as Action[],
    closeOnAction: true,
    closeOnTouchOutside: true,
    stopPropagation: [] as PropagationEvent[],
    disabled: [false, false],
};

const colorMap: { [key in ColorType]: string } = {
    red: 'var(--rbm-color-danger)',
    blue: 'var(--rbm-color-info)',
    green: 'var(--rbm-color-success)',
    orange: 'var(--rbm-color-warning)',
};

const SwipeAction = forwardRef<SwipeActionRef, SwipeActionProps>((p, ref) => {
    const props = mergeProps(defaultProps, p) as SwipeActionProps;
    const rootRef = useRef<HTMLDivElement>(null);

    const leftRef = useRef<HTMLDivElement>(null);
    const rightRef = useRef<HTMLDivElement>(null);

    const getWidth = (ref: RefObject<HTMLDivElement>) => ref?.current?.offsetWidth ?? 0;

    const getLeftWidth = () => getWidth(leftRef);
    const getRightWidth = () => getWidth(rightRef);

    const [{ x }, api] = useSpring(
        () => ({
            x: 0,
            config: { tension: 200, friction: 30 },
        }),
        [],
    );

    const draggingRef = useRef(false);
    const dragCancelRef = useRef<(() => void) | null>(null);
    const forceCancelDrag = () => {
        dragCancelRef.current?.();
        draggingRef.current = false;
    };

    const bind = useDrag(
        state => {
            const [leftDisable = false, rightDisable = false] = props.disabled ?? [false, false];
            const positionDir = state.offset[0] < 0 ? 'right' : 'left';
            if (positionDir === 'right' && rightDisable) return;
            if (positionDir === 'left' && leftDisable) return;
            dragCancelRef.current = state.cancel;
            if (!state.intentional) return;
            if (state.down) {
                draggingRef.current = true;
            }
            if (!draggingRef.current) return;
            const [offsetX] = state.offset;
            // istanbul ignore next
            if (state.last) {
                const leftWidth = getLeftWidth();
                const rightWidth = getRightWidth();
                let position = offsetX + state.velocity[0] * state.direction[0] * 50;
                if (offsetX > 0) {
                    position = Math.max(0, position);
                } else if (offsetX < 0) {
                    position = Math.min(0, position);
                } else {
                    position = 0;
                }
                const targetX = nearest([-rightWidth, 0, leftWidth], position);
                api.start({
                    x: targetX,
                });
                if (targetX !== 0) {
                    props.onActionsReveal?.(targetX > 0 ? 'left' : 'right');
                }
                window.setTimeout(() => {
                    draggingRef.current = false;
                });
            } else {
                api.start({
                    x: offsetX,
                    immediate: true,
                });
            }
        },
        {
            from: () => [x.get(), 0],
            bounds: () => {
                const leftWidth = getLeftWidth();
                const rightWidth = getRightWidth();
                return {
                    left: -rightWidth,
                    right: leftWidth,
                };
            },
            axis: 'x',
            preventScroll: true,
            pointer: { touch: true },
            triggerAllEvents: true,
        },
    );

    const close = () => {
        api.start({
            x: 0,
        });
        forceCancelDrag();
    };

    // 对外暴露的方法
    // istanbul ignore next
    useImperativeHandle(ref, () => ({
        show: (side: SideType = 'right') => {
            if (side === 'right') {
                api.start({
                    x: -getRightWidth(),
                });
            } else if (side === 'left') {
                api.start({
                    x: getLeftWidth(),
                });
            }
            props.onActionsReveal?.(side);
        },
        close,
    }));

    useEffect(() => {
        if (!props.closeOnTouchOutside) return;
        const handle = (e: Event) => {
            // istanbul ignore next
            if (x.get() === 0) {
                return;
            }
            const root = rootRef.current;
            // istanbul ignore next
            if (root && !root.contains(e.target as Node)) {
                close();
            }
        };
        document.addEventListener('touchstart', handle);
        // eslint-disable-next-line consistent-return
        return () => {
            document.removeEventListener('touchstart', handle);
        };
    }, [props.closeOnTouchOutside]);

    const renderAction = (action: Action) => {
        action = (action ?? {}) as Action;
        const color = colorMap[action?.color ?? ''] ?? action.color;
        const style = action?.style ?? {};
        return (
            <div
                key={action?.key}
                className={classNames(`${classPrefix}-actions-button`, action?.className ?? '')}
                style={{ backgroundColor: color, ...style }}
                onClick={e => {
                    // istanbul ignore next
                    if (props.closeOnAction) {
                        close();
                    }
                    action?.onClick?.(e);
                    props?.onAction?.(action, e);
                }}
            >
                {action?.text ?? ''}
            </div>
        );
    };

    return (
        <div
            className={classNames(classPrefix, props.className)}
            style={props.style}
            {...bind()}
            ref={rootRef}
            onClickCapture={e => {
                if (draggingRef.current) {
                    e.stopPropagation();
                    e.preventDefault();
                }
            }}
        >
            <animated.div className={`${classPrefix}-track`} style={{ x }}>
                {withStopPropagation(
                    props.stopPropagation,
                    <div
                        className={`${classPrefix}-actions ${classPrefix}-actions-left`}
                        ref={leftRef}
                    >
                        {props.leftActions.map(renderAction)}
                    </div>,
                )}
                <div
                    className={`${classPrefix}-content`}
                    onClickCapture={e => {
                        if (x.goal !== 0) {
                            e.preventDefault();
                            e.stopPropagation();
                            // istanbul ignore next
                            if (props.closeOnTouchOutside) {
                                close();
                            }
                        }
                    }}
                >
                    <animated.div
                        style={{
                            pointerEvents: x.to(v => (v !== 0 && x.goal !== 0 ? 'none' : 'auto')),
                        }}
                    >
                        {props.children}
                    </animated.div>
                </div>
                {withStopPropagation(
                    props.stopPropagation,
                    <div
                        className={`${classPrefix}-actions ${classPrefix}-actions-right`}
                        ref={rightRef}
                    >
                        {props.rightActions.map(renderAction)}
                    </div>,
                )}
            </animated.div>
        </div>
    );
});

export default SwipeAction;
