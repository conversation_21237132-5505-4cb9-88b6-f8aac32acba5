@import '../../var.less';
// 定义类名前缀
@class-prefix-swipe-action: ~'rbm-swipe-action';

:root {
    --rbm-swipe-action-content-bg: unset;
    --_rbm-swipe-action-content-bg: var(--rbm-gray-6);
}

.@{class-prefix-swipe-action} {
    overflow: hidden;
    touch-action: pan-y;
    &-track {
        position: relative;
        overflow: visible;
    }
    &-actions {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: stretch;
        width: auto;
        white-space: nowrap;
        &-right {
            left: 100%;
            top: 0;
            height: 100%;
        }
        &-left {
            right: 100%;
            top: 0;
            height: 100%;
        }
        &-button {
            display: flex;
            justify-content: center;
            padding: 0 16px;
            align-items: center;
        }
    }
    &-content {
        background-color: var(--rbm-swipe-action-content-bg, var(--_rbm-swipe-action-content-bg));
    }
}
