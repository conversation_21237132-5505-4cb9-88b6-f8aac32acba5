// 定义类名前缀
@class-prefix-radio: ~'rbm-radio';


:root {
    --rbm-radio-size: unset;
    --_rbm-radio-size: 16px;

    --rbm-radio-border-radio: unset;
    --_rbm-radio-border-radio: 8px;

    --rbm-radio-check-size: unset;
    --_rbm-radio-check-size: 20px;

    --rbm-radio-false-color: unset;
    --_rbm-radio-false-color: #999999;

    --rbm-radio-margin-left: unset;
    --_rbm-radio-margin-left: 6px;

    --rbm-radio-margin-right: unset;
    --_rbm-radio-margin-right: 6px;

    --rbm-radio-icon-border-width: unset;
    --_rbm-radio-icon-border-width: 5px;

    --rbm-radio-icon-false-border-width: unset;
    --_rbm-radio-icon-false-border-width: 1px;

    --rbm-radio-disabled-color: unset;
    --_rbm-radio-disabled-color: #fff298;
}


// 使用带前缀的类名变量
.@{class-prefix-radio} {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
    text-align: left;
    -webkit-tap-highlight-color: transparent;

    .icon-circle-wrap {
        margin-right: var(--rbm-radio-margin-left, var(--_rbm-radio-margin-left));

        &-left {
            margin-left: var(--rbm-radio-margin-right, var(--_rbm-radio-margin-right));
        }
    }

    &-icon-normal {
        width: var(--rbm-radio-size, var(--_rbm-radio-size));
        height: var(--rbm-radio-size, var(--_rbm-radio-size));
        border-radius: var(--rbm-radio-border-radio, var(--_rbm-radio-border-radio));
        box-sizing: border-box;
        background-color: #fff;

        &-true {
            border: var(--rbm-radio-icon-border-width, var(--_rbm-radio-icon-border-width)) solid var(--rbm-color-primary);
        }

        &-false {
            border: var(--rbm-radio-icon-false-border-width, var(--_rbm-radio-icon-false-border-width)) solid var(--rbm-radio-false-color, var(--_rbm-radio-false-color));
        }
    }

    &-icon-check {
        width: var(--rbm-radio-check-size, var(--_rbm-radio-check-size));
        height: var(--rbm-radio-check-size, var(--_rbm-radio-check-size));
        box-sizing: border-box;
        line-height: 0px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    &-disabled {
        .@{class-prefix-radio}-icon-normal {
            &-true {
                border: var(--rbm-radio-icon-border-width, var(--_rbm-radio-icon-border-width)) solid var(--rbm-radio-disabled-color, var(--_rbm-radio-disabled-color));
            }

            &-false {
                background: #f5f6fa;
                border: var(--rbm-radio-icon-false-border-width, var(--_rbm-radio-icon-false-border-width)) solid #cccccc;
            }
        }
    }

    &-input {
        display: none;
        position: absolute;
    }

    &-group {
        display: flex;

        &-row {
            flex-direction: row;
            .@{class-prefix-radio} {
                margin-right: 16px;
                &:last-child {
                    margin-right: 0px;
                }
            }
        }

        &-column {
            flex-direction: column;
            align-items: flex-start;
        }
    }
}

.@{class-prefix-radio}-rtl {
    text-align: right;

    .icon-circle-wrap {
        margin-left: var(--rbm-radio-margin-left, var(--_rbm-radio-margin-left));
        margin-right: 0;
        &-left {
            margin-left: 0;
            margin-right: var(--rbm-radio-margin-right, var(--_rbm-radio-margin-right));
        }
    }
}

.@{class-prefix-radio}-group-rtl {
    &.@{class-prefix-radio}-group-row {
        .@{class-prefix-radio} {
            margin-left: 16px;
            margin-right: 0;
            &:last-child {
                margin-left: 0px;
            }
        }
    }
}

