@import '../../var.less';

// 定义类名前缀
@class-prefix-badge: ~'rbm-badge';

:root {
    --rbm-badge-bg-color: #ff192d;
}

// 使用带前缀的类名变量
.@{class-prefix-badge} {
    display: inline-block;
    box-sizing: border-box;
    min-width: 14px;
    padding: 0 4px;
    font-family: var(--rbm-font-family);
    font-size: 11px;
    color: var(--rbm-gray-6);
    font-weight: 400;
    line-height: 14px;
    letter-spacing: 0;
    text-align: center;
    background-color: var(--rbm-badge-bg-color);
    border-radius: 8px;
    white-space: nowrap;

    &-fixed {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
        transform-origin: 100%;
    }

    &-wrapper {
        position: relative;
        display: inline-block;
        width: fit-content;
    }

    &-dot {
        width: 8px;
        min-width: 0;
        height: 8px;
        border: 0;
        border-radius: 100%;
    }

    &-direct {
        border-radius: 8px 8px 8px 1.5px;
    }
}

.@{class-prefix-badge}-rtl {
    direction: rtl;
    &.@{class-prefix-badge}-fixed {
        left: 0;
        right: auto;
        transform: translate(-50%, -50%);
    }
    &.@{class-prefix-badge}-direct {
        border-radius: 8px 8px 1.5px 8px;
    }
}
