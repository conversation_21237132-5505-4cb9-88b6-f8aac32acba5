:root {
    // 颜色
    --rbm-color-primary: #ffdd00;
    --rbm-color-primary-border: #ffeb66;
    --rbm-color-primary-disabled: #fff4ad;
    --rbm-color-primary-bg: #fffbe0;

    --rbm-color-brand: #ffdd00;
    --rbm-color-brand-border: #ffeb66;
    --rbm-color-brand-disabled: #fff4ad;
    --rbm-color-brand-bg: #fffbe0;

    --rbm-color-success: #00bf7f;
    --rbm-color-success-border: #99e5cc;
    --rbm-color-success-disabled: #d6f5ea;
    --rbm-color-success-bg: #ebfaf4;

    --rbm-color-warning: #ff6a00;
    --rbm-color-warning-border: #ffc399;
    --rbm-color-warning-disabled: #ffe7d6;
    --rbm-color-warning-bg: #fff6f0;

    --rbm-color-danger: #ff192d;
    --rbm-color-danger-border: #ffa3ab;
    --rbm-color-danger-disabled: #ffdadd;
    --rbm-color-danger-bg: #fff5f6;

    --rbm-color-info: #198cff;
    --rbm-color-info-border: #a3d1ff;
    --rbm-color-info-disabled: #daecff;
    --rbm-color-info-bg: #edf6ff;

    --rbm-gray-1: #acacac;
    --rbm-gray-2: #cccccc;
    --rbm-gray-3: #e2e2e2;
    --rbm-gray-4: #eeeeee;
    --rbm-gray-5: #f8f8f8;
    --rbm-gray-6: #ffffff;

    --rbm-half-transparent-1: rgba(0, 0, 0, 0.12);
    --rbm-half-transparent-2: rgba(0, 0, 0, 0.4);
    --rbm-half-transparent-3: rgba(0, 0, 0, 0.6);
    --rbm-half-transparent-4: rgba(0, 0, 0, 0.72);

    --rbm-page-bg: #f5f6fa;

    --rbm-gradient-primary: linear-gradient(135deg, #ffe74d 1%, #ffdd1a 100%);
    --rbm-gradient-yellow: linear-gradient(135deg, #ffe74d 1%, #ffdd1a 100%);
    --rbm-gradient-orange: linear-gradient(135deg, #ffd400 1%, #ff8c1a 100%);
    --rbm-gradient-orange-red: linear-gradient(135deg, #ffb366 0%, #ff6a00 100%);
    --rbm-gradient-red: linear-gradient(135deg, #ff7a88 0%, #ff192d 100%);
    --rbm-gradient-blue: linear-gradient(135deg, #66ccff 0%, #198cff 100%);
    --rbm-gradient-green: linear-gradient(135deg, #38ea96 0%, #00bf7f 100%);
    --rbm-gradient-cyan: linear-gradient(135deg, #2febeb 0%, #00affa 100%);

    // 文字
    --rbm-font-size-xxs: 10px;
    --rbm-font-size-xs: 11px;
    --rbm-font-size-s: 12px;
    --rbm-font-size-md: 14px;
    --rbm-font-size-l: 16px;
    --rbm-font-size-xl: 18px;
    --rbm-font-size-xxl: 21px;
    --rbm-line-height-xxs: 14px;
    --rbm-line-height-xs: 16px;
    --rbm-line-height-s: 18px;
    --rbm-line-height-md: 20px;
    --rbm-line-height-l: 22px;
    --rbm-line-height-xl: 24px;

    --rbm-font-color-text: #222222;
    --rbm-font-color-sub: #666666;
    --rbm-font-color-desc: #999999;
    --rbm-font-color-comm: #cccccc;

    --rbm-font-weight-normal: 400;
    --rbm-font-weight-bold: 500;

    --rbm-font-family-chinese: 苹方-简;
    --rbm-font-family-chinese-brand: 美团体;
    --rbm-font-family-english: SF Pro;
    --rbm-font-family-english-brand: Meituan Digital Type;
    --rbm-font-family: -apple-system, 'PingFang SC', 'Helvetica Neue', Arial, BlinkMacSystemFont,
        'microsoft yahei', 'STXihei', sans-serif;

    // 圆角
    --rbm-radius-1: 2px;
    --rbm-radius-2: 4px;
    --rbm-radius-3: 10px;
    --rbm-radius-4: 16px;
    // 阴影
    --rbm-shadow-light: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
    --rbm-shadow-medium: 0px 8px 24px 0px rgba(0, 0, 0, 0.12);
    --rbm-shadow-deep: 0px 12px 36px 0px rgba(0, 0, 0, 0.16);
    // 栅格
    --rbm-space-1: 2px;
    --rbm-space-2: 4px;
    --rbm-space-3: 6px;
    --rbm-space-4: 8px;
    --rbm-space-5: 10px;
    --rbm-space-6: 12px;
    --rbm-space-7: 14px;
    --rbm-space-8: 16px;
    --rbm-space-9: 20px;
    --rbm-space-10: 26px;

    --rbm-page-margin: 10px;
    --rbm-module-margin: 10px;
}
