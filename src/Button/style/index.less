// 定义类名前缀
@class-prefix-button: ~'rbm-button';


:root {
    --rbm-button-background: unset;
    --_rbm-button-background: unset;

    --rbm-button-active-background: unset;
    
    --rbm-button-text-color: unset;
    --_rbm-button-text-color: unset;

    --rbm-button-height: unset;
    --_rbm-button-height: unset;

    --rbm-button-width: unset;
    --_rbm-button-width: unset;

    --rbm-button-font-size: unset;
    --_rbm-button-font-size: unset;

    --rbm-button-line-height: unset;
    --_rbm-button-line-height: unset;

    --rbm-button-font-weight: unset;

    --rbm-button-border: unset;
    --_rbm-button-border: 0px;

    --rbm-button-padding: unset;
    --_rbm-button-padding: 10px 20px;

    --rbm-button-shadow: unset;
}

// 使用带前缀的类名变量
.@{class-prefix-button} {
    --max-font-size: 16px;
    --large-font-size: 14px;
    --normal-font-size: 12px;
    --middle-font-size: 12px;
    --small-font-size: 12px;
    --min-font-size: 10px;

    position: relative;
    border: 0px;
    color: var(--text-primary-color);
    padding: 10px 20px;
    border-radius: 44px;
    font-family: var(--font-family-base);
    font-size: 14px;
    text-align: center;
    font-weight: 500;
    background: var(--primary-background);
    user-select: none;
    -webkit-tap-highlight-color: transparent;

    border: var(--rbm-button-border, var(--_rbm-button-border));
    padding: var(--rbm-button-padding, var(--_rbm-button-padding));
    font-size: var(--rbm-button-font-size, var(--_rbm-button-font-size));
    line-height: var(--rbm-button-line-height, var(--_rbm-button-line-height));
    width: var(--rbm-button-width, var(--_rbm-button-width));
    height: var(--rbm-button-height, var(--_rbm-button-height));
    border-radius: 44px;
    font-weight: var(--rbm-button-font-weight, 500);
    font-family: var(--rbm-font-family);
    color: var(--rbm-button-text-color, var(--_rbm-button-text-color));
    background: var(--rbm-button-background, var(--_rbm-button-background));
    :focus {
        outline: none;
    }
    ::-moz-focus-inner {
        border-color: transparent;
    }
    &-type {
        &-text {
            background: transparent;
            color: var(--accent-orange);

            &.@{class-prefix-button}-danger {
                color: #ff192d;
                &.@{class-prefix-button}-disabled {
                    color: #ffa3ab;
                }
            }

            &.@{class-prefix-button}-disabled {
                --_rbm-button-background: transparent;
                --_rbm-button-text-color: var(--rbm-gray-1);
            }
        }

        &-primary {
            background: linear-gradient(
                -83deg,
                var(--start-linear-gradient),
                var(--end-linear-gradient)
            );

            &:active {
                background: var(--rbm-button-active-background, linear-gradient(121deg, #f5de48 0%, #f5d318 100%));
            }

            &.@{class-prefix-button}-danger {
                background: #ff192d;
                color: #ffffff;

                &:active {
                    background: #f5182b;
                }

                &.@{class-prefix-button}-disabled {
                    border: 0px;
                    background: #ffdadd;
                    color: #ffffff;
                }
            }

            &.@{class-prefix-button}-disabled {
                background: #eeeeee;
                color: #cccccc;
                border: 0px;
            }
        }

        &-default {
            .box-shadow(var(--rbm-button-shadow, var(--rbm-font-color-desc)));

            &:active {
                background: #f8f8f8;
            }

            &.@{class-prefix-button}-danger {
                --_rbm-button-text-color: var(--rbm-color-danger);
                .box-shadow(var(--rbm-button-shadow, var(--rbm-color-danger)));
                &:active {
                    background: #fff5f6;
                }

                &.@{class-prefix-button}-disabled {
                    --_rbm-button-text-color: var(--rbm-color-danger-border);
                    .box-shadow(var(--rbm-button-shadow,var(--rbm-color-danger-disabled)));
                    &:active {
                        background: var(--primary-background); //覆盖掉active，点击应该无效果。
                    }
                }
            }

            &.@{class-prefix-button}-disabled {
                --_rbm-button-background: var(--rbm-gray-6);
                --_rbm-button-text-color: var(--rbm-font-color-comm);
                .box-shadow(var(--rbm-button-shadow, var(--rbm-gray-4)));
            }
        }
    }

    &-size {
        &-max {
            font-size: var(--max-font-size);
            line-height: 22px;
            padding: 15px 28px;

            & .@{class-prefix-button}-wrap-icon {
                margin-right: 4px;
            }
        }

        &-large {
            font-size: var(--large-font-size);
            line-height: 20px;
            padding: 10px 28px;

            & .@{class-prefix-button}-wrap-icon {
                margin-right: 4px;
            }
        }

        &-normal {
            font-size: var(--normal-font-size);
            line-height: 18px;
            padding: 7px 12px;

            & .@{class-prefix-button}-wrap-icon {
                margin-right: 2px;
            }
        }

        &-middle {
            font-size: var(--middle-font-size);
            line-height: 18px;
            padding: 5px 12px;

            & .@{class-prefix-button}-wrap-icon {
                margin-right: 2px;
            }
        }

        &-small {
            font-size: var(--small-font-size);
            line-height: 18px;
            padding: 3px 12px;

            & .@{class-prefix-button}-wrap-icon {
                margin-right: 2px;
            }
        }

        &-mini {
            font-size: var(--min-font-size);
            line-height: 14px;
            padding: 3px 10px;

            & .@{class-prefix-button}-wrap-icon {
                margin-right: 1px;
            }
        }
    }

    &-shape {
        &-circle {
            overflow: hidden;
            .radius(999px);
            --_rbm-button-padding: 0;
            aspect-ratio: 1 / 1;

            &.@{class-prefix-button}-size {
                &-max {
                    width: 52px;
                    height: 52px;
                }

                &-large {
                    width: 40px;
                    height: 40px;
                }

                &-normal {
                    width: 32px;
                    height: 32px;

                    &.@{class-prefix-button}-block {
                        width: 100%;
                        height: auto;
                    }
                }

                &-middle {
                    width: 28px;
                    height: 28px;
                }

                &-small {
                    width: 24px;
                    height: 24px;
                }

                &-mini {
                    width: 20px;
                    height: 20px;
                }
            }
        }

        &-default {
            .radius(5px);
        }

        &-round {
            .radius(999px);
        }

        &-square {
            overflow: hidden;
            .radius(5px);
            --_rbm-button-padding: 0;
            aspect-ratio: 1 / 1;

            &.@{class-prefix-button}-size {
                &-max {
                    width: 52px;
                    height: 52px;
                }

                &-large {
                    width: 40px;
                    height: 40px;
                }

                &-normal {
                    width: 32px;
                    height: 32px;

                    &.@{class-prefix-button}-block {
                        width: 100%;
                        height: auto;
                    }
                }

                &-middle {
                    width: 28px;
                    height: 28px;
                }

                &-small {
                    width: 24px;
                    height: 24px;
                }

                &-mini {
                    width: 20px;
                    height: 20px;
                }
            }
        }
    }

    &-disabled {
        background: #eeeeee;
        color: #cccccc;
        border: 0px;
    }

    &-block {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }

    &-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        &-icon {
            display: flex;
            align-items: center;
        }
        &-text {
            display: flex;
            align-items: center;
        }
    }

    &-loading {
        &-icon {
            animation: loading-spin 0.8s linear infinite;
        }
    }

    &-outline-primary {
        --_rbm-button-background: transparent;
        --_rbm-button-text-color: var(--rbm-color-primary);
        .box-shadow(var(--rbm-button-shadow, var(--rbm-color-primary)));

        &:active {
            background: var(--rbm-button-active-background, var(--rbm-gray-5));
        }
    }

    &-none-primary {
        background: transparent;
        color: var(--primary-color);

        &:active {
            background: var(--rbm-button-active-background, var(--rbm-gray-5));
        }
    }

    &-none-default {
        background: transparent;
        color: var(--text-primary-color);

        &::after {
            display: none;
        }
    }

    &-outline-default {
        --_rbm-button-background: transparent;
        --_rbm-button-text-color: var(--rbm-font-color-text);
        .box-shadow(var(--rbm-button-shadow, var(--rbm-font-color-desc)));
    }
}

@keyframes loading-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

// 响应式媒体查询
@retina1x: ~'(max--moz-device-pixel-ratio: 1.49), (-webkit-max-device-pixel-ratio: 1.49), (max-device-pixel-ratio: 1.49), (max-resolution: 143dpi), (max-resolution: 1.49dppx)';
@retina2x: ~'(min--moz-device-pixel-ratio: 1.5) and (max--moz-device-pixel-ratio: 2.49), (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 2.49), (min-device-pixel-ratio: 1.5) and (max-device-pixel-ratio: 2.49), (min-resolution: 144dpi) and (max-resolution: 239dpi), (min-resolution: 1.5dppx) and (max-resolution: 2.49dppx)';
@retina3x: ~'(min--moz-device-pixel-ratio: 2.5), (-webkit-min-device-pixel-ratio: 2.5), (min-device-pixel-ratio: 2.5), (min-resolution: 240dpi), (min-resolution: 2.5dppx)';

.responsive(@media, @content) {
    @media @media {
        @content();
    }
}

.box-shadow(@border-color) {
    &:after {
        position: absolute;
        top: 0;
        left: 0;
        z-index: inherit;
        box-shadow: inset 0 0 0 1px @border-color;
        content: '\0020';
        transform-origin: 0 0;
        pointer-events: none;

        // 适配dpr进行缩放
        .responsive(@retina1x, {
        width: 100%;
        height: 100%;
      });
        .responsive(@retina2x, {
        width: 200%;
        height: 200%;
        transform: scale(0.5);
      });
        .responsive(@retina3x, {
        width: 300%;
        height: 300%;
        transform: scale(0.33333);
      });
    }
}

.radius(@radius) {
    border-radius: @radius;
    &:after {
        .responsive(@retina1x, {
          border-radius: @radius;
        });
        .responsive(@retina2x, {
          border-radius: @radius * 2;
        });
        .responsive(@retina3x, {
          border-radius: @radius * 3;
        });
    }
}