@import '../../var.less';
@class-prefix-slider: ~'rbm-slider';

.text-center {
    text-align: center;
}

:root {
    --rbm-slider-track-height: 3px;
    --rbm-slider-tick-size: 10px;
    --rbm-slider-handle-size: 24px;
    --rbm-slider-track-border-radius: 1.5px;
    --rbm-slider-active-color: var(--rbm-color-primary);
    --rbm-slider-inactive-color: var(--rbm-gray-5);
    --rbm-slider-disabled-active-color: var(--rbm-color-primary-disabled);
    --rbm-slider-disabled-inactive-color: var(--rbm-gray-5);
    --rbm-slider-handle-background: var(--rbm-gray-6);
    --rbm-slider-handle-shadow: 0px 4px 12px 0px #0000001e, 0px 0px 2px 0px #00000028;
}

.@{class-prefix-slider} {
    padding: 5px 12px;
    list-style: none;
    user-select: none;

    &-track-container {
        padding: 10px 0;
    }
    &-track {
        position: relative;
        width: 100%;
        height: var(--rbm-slider-track-height);
        background-color: var(--rbm-slider-inactive-color);
        border-radius: var(--rbm-slider-track-border-radius);
    }

    &-fill {
        position: absolute;
        z-index: 1;
        height: var(--rbm-slider-track-height);
        background-color: var(--rbm-slider-active-color);
        border-radius: var(--rbm-slider-track-border-radius);
    }

    &-ticks {
        position: absolute;
        width: 100%;
        height: var(--rbm-slider-track-height);
        background: transparent;
    }

    &-tick {
        position: absolute;
        width: var(--rbm-slider-tick-size);
        height: var(--rbm-slider-tick-size);
        background-color: var(--rbm-slider-inactive-color);
        border-radius: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        &-active {
            background-color: var(--rbm-slider-active-color);
        }
    }

    &-handle {
        cursor: grab;
        touch-action: none;
        position: absolute;
        z-index: 2;
        width: var(--rbm-slider-handle-size);
        height: var(--rbm-slider-handle-size);
        border-radius: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    &-handle-icon {
        width: var(--rbm-slider-handle-size);
        height: var(--rbm-slider-handle-size);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: var(--rbm-slider-handle-background);
        box-shadow: var(--rbm-slider-handle-shadow);
    }

    // mark
    &-mark {
        width: 100%;
        overflow: visible;
        position: relative;
    }
    &-mark-item {
        color: var(--rbm-font-color-desc);
        font-weight: var(--rbm-font-weight-normal);
        font-size: var(--rbm-font-size-s);
        line-height: var(--rbm-line-height-s);
        letter-spacing: 0px;
        text-align: center;
        text-decoration: none;
        position: absolute;
        display: inline-block;
        word-break: keep-all;
        user-select: none;
        transform: translateX(-50%);
    }

    &-disabled {
        .@{class-prefix-slider}-fill {
            background-color: var(--rbm-slider-disabled-active-color);
        }
        .@{class-prefix-slider}-track {
            background-color: var(--rbm-slider-disabled-inactive-color);
        }
        .@{class-prefix-slider}-handle {
            cursor: not-allowed;
        }
        .@{class-prefix-slider}-tick {
            cursor: not-allowed;
            box-shadow: none;
            background-color: var(--rbm-slider-disabled-inactive-color);
            &-active {
                background-color: var(--rbm-slider-disabled-active-color);
            }
        }
    }

    &-vertical {
        padding: 12px 5px;
        height: 100%;

        .@{class-prefix-slider}-track-container {
            padding: 0 20px;
            height: 100%;
        }

        .@{class-prefix-slider}-track {
            width: var(--rbm-slider-track-height);
            height: 100%;
        }

        .@{class-prefix-slider}-fill {
            width: var(--rbm-slider-track-height);
        }

        .@{class-prefix-slider}-tick {
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .@{class-prefix-slider}-ticks {
            height: 100%;
        }

        .@{class-prefix-slider}-mark {
            height: 100%;
            width: 0;
            transform: translateY(-100%);
        }

        .@{class-prefix-slider}-mark-item {
            transform: translate(-100%, -50%);
        }

        .@{class-prefix-slider}-handle {
            left: 50%;
        }

        // .@{class-prefix-slider}-handle {
        //   top: auto;
        //   right: 50%;
        //   bottom: 0;
        //   transform: translate3d(50%, 50%, 0);
        // }

        .@{class-prefix-slider}-handle {
            top: 0;
            right: 50%;
            left: auto;
            transform: translate3d(50%, -50%, 0);
        }

        // use pseudo element to expand click area
        &::before {
            top: 0;
            // right: calc(var(--rv-padding-xs) * -1);
            bottom: 0;
            // left: calc(var(--rv-padding-xs) * -1);
        }
    }
}
