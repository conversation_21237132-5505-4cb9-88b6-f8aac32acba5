import React, { CSSProperties, useMemo, useRef } from 'react';
import classNames from 'classnames';
import { Decimal } from 'decimal.js';
import { SliderProps, RtlType, RtlTypeEnum } from './interface';
import Marks from './marks';
import Ticks from './ticks';
import { mergeProps } from '../utils/with-default-props';
import Handle from './handle';
import { usePropsValue } from '../utils/use-props-value';
import { nearest } from '../utils/nearest';
import { addUnit } from '../utils/unit';
import { GlobalConfigContext } from '../ConfigProvider';

export type SliderValue = number | [number, number];

const classPrefix = 'rbm-slider';

const directionMap: { [key: string]: RtlType } = {
    RTL: RtlTypeEnum.rtl,
    rtl: RtlTypeEnum.rtl,
    LTR: RtlTypeEnum.ltr,
    ltr: RtlTypeEnum.ltr,
};

const defaultProps = {
    min: 0,
    max: 100,
    step: 1,
    ticks: false,
    range: false,
    disabled: false,
    popover: false,
    residentPopover: false,
    vertical: false,
};

function sortValue(val: [number, number]): [number, number] {
    return val.sort((a, b) => a - b);
}
function convertValue(value: SliderValue, range: boolean, min: number): [number, number] {
    if (range && !Array.isArray(value)) {
        console.warn(
            'Slider: When `range` prop is enabled, the `value` prop should be an array, like: [10, 50]',
        );
        return [0, value];
    }

    return (range ? value : [min, value]) as any;
}
function alignValue(value: number, decimalLen: number) {
    const factor = 10 ** decimalLen;
    return Math.round(value * factor) / factor;
}

function getDecimalLen(n: number) {
    return (`${n}`.split('.')[1] || '').length;
}

function reverseValue(value: [number, number], range: boolean, step: number): SliderValue {
    const mergedDecimalLen = Math.max(
        getDecimalLen(step),
        getDecimalLen(value[0]),
        getDecimalLen(value[1]),
    );
    return range
        ? (value.map(v => alignValue(v, mergedDecimalLen)) as [number, number])
        : alignValue(value[1], mergedDecimalLen);
}

function Component(p: SliderProps) {
    const props: SliderProps = mergeProps(defaultProps, p);
    const { marks, range, min, max, step, value, disabled, readOnly, vertical, reverse } = props;
    // 滑块轴的ref
    const trackRef = useRef<HTMLDivElement>(null);
    // 拖拽锁，记录是否在拖拽中。可能两个滑块同时滑动，所以不使用boolean
    const dragLockRef = useRef(0);
    const valueBeforeDragRef = useRef<[number, number]>();

    const { direction: rawDirection } = React.useContext(GlobalConfigContext);
    const direction = useMemo(() => directionMap[rawDirection], [rawDirection]);
    const trueReverse = !vertical && direction === RtlTypeEnum.rtl ? !reverse : reverse;

    let propsValue: SliderValue | undefined = value;
    if (range && typeof value === 'number') {
        console.warn(
            'Slider: When `range` prop is enabled, the `value` prop should be an array, like: [0, 0]',
        );
        propsValue = [0, value];
    }
    const [rawValue, setRawValue] = usePropsValue<SliderValue>({
        value: propsValue,
        defaultValue: props.defaultValue ?? (props.range ? [min, min] : min),
        onChange: props.onChange,
    });

    const sliderValue = sortValue(convertValue(rawValue, range, min));
    function setSliderValue(value: [number, number]) {
        const next = sortValue(value);
        const current = sliderValue;
        if (next[0] === current[0] && next[1] === current[1]) return;
        setRawValue(reverseValue(next, range, step));
    }

    function onChangeComplete(value: [number, number]) {
        props.onChangeComplete?.(reverseValue(value, range, step));
    }

    function onDragStart(value: [number, number]) {
        props.onDragStart?.(reverseValue(value, range, step));
    }
    function onDragEnd(value: [number, number]) {
        props.onDragEnd?.(reverseValue(value, range, step));
    }

    const fillSize = `${(100 * (sliderValue[1] - sliderValue[0])) / (max - min)}%`;
    const fillStart = `${(100 * (sliderValue[0] - min)) / (max - min)}%`;

    // 控制背景进度条高度
    const barHeightStyle = useMemo(() => {
        const heightStyle: CSSProperties = {};
        const crossAxis = props.vertical ? 'width' : 'height';
        // 没有else，走不到else，所以加白
        // istanbul ignore next
        if (props.barHeight) {
            heightStyle[crossAxis] = addUnit(props.barHeight);
        }
        return heightStyle;
    }, [props.barHeight, props.vertical]);

    // 刻度大小
    const tickSizeStyle = useMemo(() => {
        const sizeStyle: CSSProperties = {};
        // 没有else，走不到else，所以加白
        // istanbul ignore next
        if (props.tickSize) {
            sizeStyle.height = props.tickSize;
            sizeStyle.width = props.tickSize;
        }
        return sizeStyle;
    }, [props.tickSize]);

    // 非激活态进度条
    const wrapperStyle = useMemo(
        () => ({
            ...barHeightStyle,
            background: props.disabled ? props.disabledInactiveColor : props.inactiveColor,
        }),
        [props.disabled, props.disabledInactiveColor, props.inactiveColor, barHeightStyle],
    );

    // 激活的进度条
    const activeTrackStyle = useMemo<CSSProperties>(() => {
        const mainAxis = props.vertical ? 'height' : 'width';

        const trackStyle: CSSProperties = {
            ...barHeightStyle,
            [mainAxis]: fillSize,
            background: props.disabled ? props.disabledActiveColor : props.activeColor,
        };

        if (dragLockRef.current) {
            trackStyle.transition = 'none';
        }
        const getPositionKey = () => {
            if (props.vertical) {
                return trueReverse ? 'bottom' : 'top';
            }
            return trueReverse ? 'right' : 'left';
        };

        trackStyle[getPositionKey()] = fillStart;

        return trackStyle;
    }, [
        props.vertical,
        props.disabled,
        props.disabledActiveColor,
        props.activeColor,
        trueReverse,
        barHeightStyle,
        fillSize,
        fillStart,
    ]);

    // 滑动轴上可取值点。传入了marks则取marks的key，否则根据min、max、step计算
    const pointList = useMemo(() => {
        function calculate(min, step, i) {
            const minDecimal = new Decimal(min);
            const stepDecimal = new Decimal(step);

            return minDecimal.plus(stepDecimal.times(i)).toNumber();
        }

        const countList = marks
            ? Object.keys(marks)
                  .map(parseFloat)
                  .sort((a, b) => a - b)
            : Array.from({ length: (max - min) / step + 1 }, (_, i) => calculate(min, step, i));

        const actualPointList = countList.map((count, index) => {
            const value = trueReverse ? countList[countList.length - index - 1] : countList[index];

            let offset = Math.abs(value - min) / (max - min);
            if (trueReverse) offset = 1 - offset;

            const mark = marks?.[value] || null;
            return {
                count,
                value,
                isActive: sliderValue[0] <= value && sliderValue[1] >= value,
                offset: `${offset * 100}%`,
                mark,
            };
        });
        return actualPointList;
    }, [marks, max, min, step, trueReverse, sliderValue]);

    function getValueByPosition(position: number) {
        // eslint-disable-next-line no-nested-ternary
        const newPosition = position < min ? min : position > max ? max : position;

        let nodeCount = min;
        nodeCount = nearest(
            pointList.map(item => item.value),
            newPosition,
        );
        return nodeCount;
    }

    const onTrackClick = (event: React.MouseEvent) => {
        // 边界条件判断，加白
        // istanbul ignore next
        if (dragLockRef.current > 0) return;
        event.stopPropagation();
        if (disabled || readOnly) return;
        const track = trackRef.current;
        // 边界条件判断，加白
        // istanbul ignore next
        if (!track) return;

        const getPosition = () => {
            let left: number;
            let right: number;
            if (vertical) {
                right = event.clientY;
                left = trueReverse
                    ? track.getBoundingClientRect().bottom
                    : track.getBoundingClientRect().top;
            } else {
                right = event.clientX;
                left = trueReverse
                    ? track.getBoundingClientRect().right
                    : track.getBoundingClientRect().left;
            }

            return (
                (Math.abs(left - right) /
                    Math.ceil(vertical ? track.offsetHeight : track.offsetWidth)) *
                    (max - min) +
                min
            );
        };

        const targetValue = getValueByPosition(getPosition());

        let nextSliderValue: [number, number];
        if (props.range) {
            // 移动的滑块采用就近原则
            if (Math.abs(targetValue - sliderValue[0]) > Math.abs(targetValue - sliderValue[1])) {
                nextSliderValue = [sliderValue[0], targetValue];
            } else {
                nextSliderValue = [targetValue, sliderValue[1]];
            }
        } else {
            nextSliderValue = [props.min, targetValue];
        }
        setSliderValue(nextSliderValue);
        onChangeComplete(nextSliderValue);
    };

    // first/last是useDrag的state中的参数，表示一次拖拽的开始/结束
    const onDrag = (position, first, last, index) => {
        if (first) {
            dragLockRef.current += 1;
            valueBeforeDragRef.current = sliderValue;
            onDragStart(sliderValue);
        }

        const val = getValueByPosition(position);
        const valueBeforeDrag = valueBeforeDragRef.current;
        // 边界条件判断，加白
        // istanbul ignore next
        if (!valueBeforeDrag) return;
        const next = [...valueBeforeDrag] as [number, number];
        next[index] = val;
        setSliderValue(next);

        if (last) {
            onDragEnd(next);
            onChangeComplete(next);
            // 异步调用，无法走到，所以加白
            // istanbul ignore next
            window.setTimeout(() => {
                // istanbul ignore next
                dragLockRef.current -= 1;
            }, 100);
        }
    };

    const renderHandle = (index: number, icon?: React.ReactNode, handle?: React.ReactNode) => (
        <Handle
            key={index}
            value={sliderValue[index]}
            classPrefix={classPrefix}
            min={min}
            max={max}
            disabled={disabled || readOnly}
            trackRef={trackRef}
            tooltip={props.tooltip}
            icon={icon}
            handle={handle}
            vertical={vertical}
            reverse={trueReverse}
            iconSize={props.iconSize}
            onDrag={(position, first, last) => onDrag(position, first, last, index)}
            aria-label={props['aria-label']}
        />
    );

    return (
        <div
            className={classNames(classPrefix, props.className, {
                [`${classPrefix}-disabled`]: disabled,
                [`${classPrefix}-vertical`]: vertical,
            })}
            style={props.style}
        >
            <div className={`${classPrefix}-track-container`} onClick={onTrackClick}>
                {/* 背景条 */}
                <div
                    style={wrapperStyle}
                    ref={trackRef}
                    className={`${classPrefix}-track`}
                    onClick={onTrackClick}
                >
                    {/* 激活的进度条，纯展示用 */}
                    <div className={`${classPrefix}-fill`} style={activeTrackStyle} />
                    {/* 标记点，纯展示用 */}
                    {props.ticks ? (
                        <Ticks
                            activeColor={disabled ? props.disabledActiveColor : props.activeColor}
                            inactiveColor={
                                disabled ? props.disabledInactiveColor : props.inactiveColor
                            }
                            barHeightStyle={barHeightStyle}
                            tickSizeStyle={tickSizeStyle}
                            classPrefix={classPrefix}
                            pointList={pointList}
                            vertical={vertical}
                        />
                    ) : null}
                    {/* 抓取点，交互用。最终值(抓取点的位置)由handle位置和pointList决定 */}
                    {range
                        ? [renderHandle(0, props.leftIcon), renderHandle(1, props.rightIcon)]
                        : renderHandle(1, props.icon)}
                </div>
            </div>
            {/* 轴坐标，纯展示用 */}
            {marks ? (
                <Marks pointList={pointList} classPrefix={classPrefix} vertical={vertical} />
            ) : null}
        </div>
    );
}

export default Component;
