@import '../../var.less';

@class-prefix-swiper: ~'rbm-swiper';

:root {
    --rbm-swiper-border-radius: unset;
    --_rbm-swiper-border-radius: 0;

    --rbm-swiper-height: unset;
    --_rbm-swiper-height: auto;

    --rbm-swiper-width: unset;
    --_rbm-swiper-width: 100%;


    --rbm-swiper-track-padding: unset;
    --_rbm-swiper-track-padding: 0;

    --rbm-swiper-slide-size: unset;
    --_rbm-swiper-slide-size: 100%;

    --rbm-swiper-track-offset: unset;
    --_rbm-swiper-track-offset: 0;
}

.@{class-prefix-swiper} {
    display: block;
    width: var(--rbm-swiper-width, --_rbm-swiper-width);
    height: var(--rbm-swiper-height, --_rbm-swiper-height);
    position: relative;
    border-radius: var(--rbm-swiper-border-radius, --_rbm-swiper-border-radius);
    z-index: 0;
    overflow: hidden;
    &-track {
        width: 100%;
        height: 100%;
        white-space: nowrap;
        padding: var(--rbm-swiper-track-padding, --_rbm-swiper-track-padding);
        &-allow-touch-move {
            cursor: grab;
        }
        &-inner {
            width: 100%;
            height: 100%;
            overflow: visible;
            position: relative;
            display: flex;
            flex-wrap: nowrap;
        }
    }
}

.@{class-prefix-swiper}-slide,
.@{class-prefix-swiper}-slide-placeholder {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
    white-space: unset;
    flex: none;
}

.@{class-prefix-swiper}-item {
    display: block;
    width: 100%;
    height: 100%;
    white-space: normal;
}

.@{class-prefix-swiper}-horizontal {
    .@{class-prefix-swiper}-track-allow-touch-move {
        touch-action: pan-y;
    }
    .@{class-prefix-swiper}-indicator {
        position: absolute;
        bottom: 6px;
        left: 50%;
        transform: translateX(-50%);
    }
    .@{class-prefix-swiper}-track {
        transform: translateX(var(--rbm-swiper-track-offset, --_rbm-swiper-track-offset));
        &-inner {
            flex-direction: row;
            width: var(--rbm-swiper-slide-size, --_rbm-swiper-slide-size);
        }
    }
}
.@{class-prefix-swiper}-vertical {
    .@{class-prefix-swiper}-track-allow-touch-move {
        touch-action: pan-x;
    }
    .@{class-prefix-swiper}-indicator {
        position: absolute;
        right: 6px;
        top: 50%;
        transform: translateY(-50%);
    }
    .@{class-prefix-swiper}-track {
        transform: translateY(var(--rbm-swiper-track-offset, --_rbm-swiper-track-offset));
        &-inner {
            flex-direction: column;
            height: var(--rbm-swiper-slide-size, --_rbm-swiper-slide-size);
        }
    }
}

.@{class-prefix-swiper}-rtl {
    &.@{class-prefix-swiper}-horizontal {
        .@{class-prefix-swiper}-indicator { 
            left: 6px;
            right: 0;
        }
    }
}
