@import '../../var.less';

@class-prefix-switch: ~'rbm-switch';

:root {
    --_rbm-switch-height: 28px;

    --_rbm-switch-width: 48px;

    --_rbm-switch-border-width: 2px;

    --_rbm-switch-checked-color: var(--rbm-color-primary);
}

.@{class-prefix-switch} {
    --rbm-switch-checked-color: var(--_rbm-switch-checked-color);
    --rbm-switch-height: var(--_rbm-switch-height);
    --rbm-switch-width: var(--_rbm-switch-width);
    --rbm-switch-border-width: var(--_rbm-switch-border-width);
    user-select: none;
    touch-action: none;
    -webkit-tap-highlight-color: transparent;

    &&-rtl {
        direction: rtl;
        .@{class-prefix-switch}-handle {
            right: var(--rbm-switch-border-width);
        }
        .@{class-prefix-switch}-inner {
            margin: 0 calc(var(--rbm-switch-height) - var(--rbm-switch-border-width) + 5px) 0 8px;
        }
        &.@{class-prefix-switch}-checked {
            .@{class-prefix-switch}-handle {
                right: calc(100% - (var(--rbm-switch-height) - var(--rbm-switch-border-width)));
            }
            .@{class-prefix-switch}-inner {
                margin: 0 8px 0
                    calc(var(--rbm-switch-height) - var(--rbm-switch-border-width) + 5px);
                color: #ffffff;
            }
        }
    }
    // 小尺寸
    &-small {
        --_rbm-switch-height: 22px;
        --_rbm-switch-width: 38px;
    }
    display: inline-block;
    vertical-align: middle;
    box-sizing: border-box;
    position: relative;
    align-self: center;
    cursor: pointer;
    border: 0;
    border-radius: 31px;
    background-color: unset;
    padding: 0;
    input {
        display: none;
    }

    &-checkbox {
        min-width: var(--rbm-switch-width);
        height: var(--rbm-switch-height);
        box-sizing: border-box;
        border-radius: 31px;
        background: #eeeeee;
        z-index: 0;
        overflow: hidden;
        line-height: var(--rbm-switch-height);

        &:before {
            content: ' ';
            position: absolute;
            left: var(--rbm-switch-border-width);
            top: var(--rbm-switch-border-width);
            width: calc(100% - 2 * var(--rbm-switch-border-width));
            height: calc(var(--rbm-switch-height) - 2 * var(--rbm-switch-border-width));
            border-radius: calc(var(--rbm-switch-height) - 2 * var(--rbm-switch-border-width));
            box-sizing: border-box;
            background: #eeeeee;
            z-index: 1;
            transition: all 200ms;
            transform: scale(1);
        }
    }

    &-handle {
        display: flex;
        justify-content: center;
        align-items: center;
        width: calc(var(--rbm-switch-height) - 2 * var(--rbm-switch-border-width));
        height: calc(var(--rbm-switch-height) - 2 * var(--rbm-switch-border-width));
        border-radius: calc(var(--rbm-switch-height) - 2 * var(--rbm-switch-border-width));
        background: #ffffff;
        position: absolute;
        z-index: 2;
        top: var(--rbm-switch-border-width);
        left: var(--rbm-switch-border-width);
        transition: all 200ms;
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.2), 0 2px 11.5px 0 rgba(0, 0, 0, 0.08),
            -1px 2px 2px 0 rgba(0, 0, 0, 0.1);
    }

    &-inner {
        position: relative;
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 8px 0 calc(var(--rbm-switch-height) - var(--rbm-switch-border-width) + 5px);
        height: 100%;
        color: #999999;
        transition: margin 200ms;
        font-size: 14px;
    }

    /* 选中状态 */
    &.@{class-prefix-switch}-checked {
        .@{class-prefix-switch}-checkbox {
            background: var(--rbm-switch-checked-color);
            &:before {
                transform: scale(0);
            }
        }
        .@{class-prefix-switch}-handle {
            left: calc(100% - (var(--rbm-switch-height) - var(--rbm-switch-border-width)));
        }

        .@{class-prefix-switch}-inner {
            margin: 0 calc(var(--rbm-switch-height) - var(--rbm-switch-border-width) + 5px) 0 8px;
            color: #ffffff;
        }
    }

    /* 禁用状态 */
    &.@{class-prefix-switch}-disabled {
        cursor: not-allowed;
        opacity: 0.4;
    }

    /* 右对齐 */
}
