@import '../../var.less';

@class-prefix-loading: ~'rbm-loading';

:root {
    --rbm-loading-color: #999999;
    --rbm-loading-size: 20px;
    --rbm-loading-bg-color: rgba(0, 0, 0, 0.2);
}

.@{class-prefix-loading} {
    &-rtl {
        direction: rtl;
    }
    &-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--rbm-loading-bg-color);
        pointer-events: auto;
        width: 100%;
        height: 100%;
        z-index: 1050;
    }

    &-wrap {
        height: auto;
        display: inline-block;
        text-align: center;
        font-size: 14px;
        color: var(--rbm-loading-color);
    }
    &-wrap-fullscreen {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, 0%);
        margin: 0;
    }
    &-wrap-is-container {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        margin: 0;
    }
    &-item-wrap {
        position: relative;
        display: inline-block;
        width: var(--rbm-loading-size);
        height: var(--rbm-loading-size);
        .loading-item {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }
        .loading-item::before {
            width: 6%;
            height: 24%;
            min-width: 1px;
            min-height: 5px;
            border-radius: 0;
            content: '';
            display: block;
            margin: 0 auto;
            animation: fade 1.2s infinite ease-in-out both;
            --dynamic-color: var(--rbm-loading-color);
        }

        .loading-item0 {
            transform: rotate(0deg);
        }
        .loading-item0::before {
            animation-delay: -1.2s;
        }
        .loading-item1 {
            transform: rotate(30deg);
        }
        .loading-item1::before {
            animation-delay: -1.1s;
        }
        .loading-item2 {
            transform: rotate(60deg);
        }
        .loading-item2::before {
            animation-delay: -1s;
        }
        .loading-item3 {
            transform: rotate(90deg);
        }
        .loading-item3::before {
            animation-delay: -0.9s;
        }
        .loading-item4 {
            transform: rotate(120deg);
        }
        .loading-item4::before {
            animation-delay: -0.8s;
        }
        .loading-item5 {
            transform: rotate(150deg);
        }
        .loading-item5::before {
            animation-delay: -0.7s;
        }
        .loading-item6 {
            transform: rotate(180deg);
        }
        .loading-item6::before {
            animation-delay: -0.6s;
        }
        .loading-item7 {
            transform: rotate(210deg);
        }
        .loading-item7::before {
            animation-delay: -0.5s;
        }
        .loading-item8 {
            transform: rotate(240deg);
        }
        .loading-item8::before {
            animation-delay: -0.4s;
        }
        .loading-item9 {
            transform: rotate(270deg);
        }
        .loading-item9::before {
            animation-delay: -0.3s;
        }
        .loading-item10 {
            transform: rotate(300deg);
        }
        .loading-item10::before {
            animation-delay: -0.2s;
        }
        .loading-item11 {
            transform: rotate(330deg);
        }
        .loading-item11::before {
            animation-delay: -0.1s;
        }
        @keyframes fade {
            0% {
                background: var(--dynamic-color);
            }
            50% {
                background: transparent;
            }
            100% {
                background: var(--dynamic-color);
            }
        }

        // @keyframes fadeWhite {
        //     0% {
        //         background: rgba(255, 255, 255, 0);
        //     }
        //     50% {
        //         background: rgba(255, 255, 255, 1);
        //     }
        //     100% {
        //         background: rgba(255, 255, 255, 0);
        //     }
        // }
    }
    &-item-circle {
        .loading-item::before {
            min-width: 4px;
            min-height: 4px;
            border-radius: 100%;
        }
    }
    &-item-hline {
        .loading-item::before {
            width: 12%;
            height: 3%;
            min-width: 3px;
            min-height: 1px;
            border-radius: 0;
        }
    }
    &-item-primary {
        --rbm-loading-size: 38px;
        position: relative;
        display: inline-block;
        animation: spin 0.8s infinite steps(8);
        .loading-item {
            position: absolute;
            width: 50%;
            height: 100%;
            top: 0;
            left: 50%;
            overflow: hidden;
            transform-origin: 0 50%;
        }
        .loading-item::after {
            content: ' ';
            position: absolute;
            top: 0;
            left: -100%;
            border-style: solid;
            border-width: 3px;
            border-color: #d2d2d2 transparent transparent;
            border-radius: var(--rbm-loading-size);
            transform: rotate(-6deg);
            --dynamic-color: var(--rbm-loading-color);
        }

        .loading-item0 {
            transform: rotate(3deg);
        }
        .loading-item0::after {
            border-top-color: var(--dynamic-color);
        }
        .loading-item1 {
            transform: rotate(48deg);
        }
        .loading-item1::after {
            border-top-color: var(--dynamic-color);
        }
        .loading-item2 {
            transform: rotate(93deg);
        }

        .loading-item3 {
            transform: rotate(138deg);
        }

        .loading-item4 {
            transform: rotate(183deg);
        }

        .loading-item5 {
            transform: rotate(228deg);
        }

        .loading-item6 {
            transform: rotate(273deg);
        }

        .loading-item7 {
            transform: rotate(318deg);
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    }
    &-hide {
        display: none;
    }
    &-box {
        padding: 13px;
        .loading-tip:empty {
            white-space: nowrap;
            margin: auto;
        }
        .loading-tip-right {
            white-space: nowrap;
            margin: auto 8px;
        }

        .loading-tip-left {
            white-space: nowrap;
            margin: auto 8px;
        }
    }
    &-box-black {
        background: rgba(0, 0, 0, 0.6);
        border-radius: 6px;
    }
    &-box-flex {
        display: flex;
    }
}
