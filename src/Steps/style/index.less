@import '../../var.less';
@import '../../Theme/index.less';

@class-prefix-steps: ~'rbm-steps';

:root {
    // Steps组件主题变量
    --rbm-steps-description-font-size: unset;
    --_rbm-steps-description-font-size: var(--rbm-font-size-s, 12px);
    --rbm-steps-title-font-size: unset;
    --_rbm-steps-title-font-size: var(--rbm-font-size-l, 16px);
    --rbm-steps-icon-font-size: unset;
    --_rbm-steps-icon-font-size: var(--rbm-font-size-s, 12px);
}
@process-icon-color: linear-gradient(135deg, #ffe74d 0%, #ffdd1a 100%);
@wait-icon-color: #ccc;
@wait-title-color: #ccc;
@finish-icon-color: var(--accent-green);
@error-icon-color: var(--accent-red);

.@{class-prefix-steps} {
    font-size: 0;
    width: 100%;
    line-height: 1.5;
    display: flex;

    &,
    * {
        box-sizing: border-box;
    }
}

.@{class-prefix-steps}-item {
    position: relative;
    display: inline-block;
    vertical-align: top;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;

    &-container[role='button'] {
        cursor: pointer;
        transition: opacity 0.3s;

        &:hover {
            opacity: 0.7;
        }
    }

    &:last-child {
        flex: none;
    }

    &:last-child &-tail,
    &:last-child &-title:after {
        display: none;
    }

    &-main {
        position: relative;
        display: inline-block;
    }

    &-icon,
    &-content {
        display: inline-block;
        vertical-align: top;
    }

    &-icon {
        width: 20px;
        height: 20px;
        line-height: 20px;
        border-radius: 10px;
        text-align: center;
        font-size: 14px;
        margin-right: 6px;
        margin-top: 4px;
        background-color: @wait-icon-color;
        transition: background-color 0.3s, border-color 0.3s;

        > .@{class-prefix-steps}-icon {
            font-size: var(--rbm-steps-icon-font-size, var(--_rbm-steps-icon-font-size));
            font-weight: 600;
            color: var(--primary-background);
        }
    }
    &-tail {
        position: absolute;
        left: 0;
        width: 100%;
        top: 12px;
        padding: 0 10px;
        &:after {
            content: '';
            display: inline-block;
            height: 1px;
            width: 100%;
            background: transparent;
            border-left: 1px solid #eeeeee;
        }
    }

    &-title {
        font-size: var(--rbm-steps-title-font-size, var(--_rbm-steps-title-font-size));
        color: var(--text-primary-color);
        font-weight: bold;
        display: inline-block;
        padding-right: 10px;
        position: relative;
        line-height: 1;
        &:after {
            content: '';
            height: 1px;
            width: 1000px;
            display: block;
            position: absolute;
            border-top: 1px solid #eeeeee;
            top: 8px;
            left: 100%;
        }
    }
    &-description,
    &-contents {
        font-size: var(--rbm-steps-description-font-size, var(--_rbm-steps-description-font-size));
        margin-top: 5px;
        color: var(--secondary-color);
    }
    &-content {
        font-size: 14px;
        margin-top: 4px;
    }
    &-contents {
        margin-top: 4px;
    }
    &-label {
        font-size: 14px;
        position: absolute;
        margin-top: 5px;
        margin-right: 6px;
        width: 40px;
        text-align: right;
    }

    &-wait {
        .@{class-prefix-steps}-item {
            &-title,
            &-description {
                color: @wait-title-color;
            }
            &-icon {
                background-color: @wait-icon-color;
            }
        }
    }
    &-finish &-icon {
        background: @finish-icon-color;
        width: 20px;
        height: 20px;
    }
    &-process &-icon {
        background: @process-icon-color;
        .@{class-prefix-steps}-icon {
            color: #222220;
        }
    }
    &-error {
        .@{class-prefix-steps}-item {
            &-title,
            &-description {
                color: @error-icon-color;
            }
            &-icon {
                background: @error-icon-color;
                width: 20px;
                height: 20px;
            }
        }
    }

    &-disabled {
        cursor: not-allowed;
    }
    &-custom &-icon {
        background: 0 0;
        .@{class-prefix-steps}-icon {
            color: var(--primary-color);
        }
    }
}

.@{class-prefix-steps}-dot {
    .@{class-prefix-steps}-item {
        &-icon {
            .@{class-prefix-steps}-icon-dot {
                width: 6px;
                height: 6px;
                border: 1.5px solid #222222;
                display: inline-block;
                border-radius: 3px;
                margin-top: 11.4px;
            }
        }

        &-tail {
            &:after {
                border-left-style: dashed;
            }
        }

        &-title::after {
            border-top-style: dashed;
        }

        &-wait {
            .@{class-prefix-steps}-item {
                &-icon {
                    .@{class-prefix-steps}-icon-dot {
                        border-color: @wait-icon-color;
                    }
                }
            }
        }
        &-error {
            .@{class-prefix-steps}-item {
                &-icon {
                    .@{class-prefix-steps}-icon-dot {
                        border-color: @error-icon-color;
                    }
                }
            }
        }
        &:not(.@{class-prefix-steps}-item-custom):not(.@{class-prefix-steps}-item-custom-icon) {
            .@{class-prefix-steps}-item-icon {
                margin-top: 0px;
                width: 6px;
            }
        }
        &:not(.@{class-prefix-steps}-item-custom-icon) {
            .@{class-prefix-steps}-item-icon {
                background: transparent;
            }
        }
    }
}

.@{class-prefix-steps}-horizontal:not(.@{class-prefix-steps}-label-vertical) {
    .@{class-prefix-steps}-item {
        margin-right: 10px;
        &:last-child {
            margin-right: 0;
        }
        &-tail {
            display: none;
        }
        &-description {
            max-width: 100px;
            white-space: normal;
        }
    }
}

.@{class-prefix-steps}-vertical {
    display: flex;
    flex-direction: column;
    .@{class-prefix-steps}-item {
        display: block;
        flex: 1 0 auto;
        padding-inline-start: 0;
        overflow: visible;
        white-space: normal;

        &-icon {
            float: left;
            margin-top: 6px;
        }
        &-content {
            margin-top: 6px;
            margin-bottom: 32px;
            overflow: hidden;
            display: block;
        }
        &-title {
            &:after {
                display: none;
            }
        }
        &-tail {
            position: absolute;
            left: 9.5px;
            top: 5px;
            width: 1px;
            height: 100%;
            padding: 24px 0 4px 0;
            &:after {
                height: 100%;
                width: 1px;
            }
        }
    }

    .@{class-prefix-steps}-item-with-label {
        .@{class-prefix-steps}-item-main {
            margin-left: 46px;
        }
    }

    &.@{class-prefix-steps}-dot {
        .@{class-prefix-steps}-item {
            .@{class-prefix-steps}-item-tail {
                top: 18px;
                padding: 1px 0 7px 0;
            }
        }
        .@{class-prefix-steps}-item-custom,
        .@{class-prefix-steps}-item-custom-icon {
            .@{class-prefix-steps}-item-tail {
                padding-top: 8px;
            }
        }
        .@{class-prefix-steps}-item-before-custom {
            .@{class-prefix-steps}-item-tail {
                padding-top: 6px;
                top: 14px;
            }
        }
        .@{class-prefix-steps}-item-custom.@{class-prefix-steps}-item-before-custom,
        .@{class-prefix-steps}-item-custom-icon.@{class-prefix-steps}-item-before-custom {
            .@{class-prefix-steps}-item-tail {
                padding-top: 12px;
                top: 14px;
            }
        }
        &.@{class-prefix-steps}-custom {
            .@{class-prefix-steps}-item:not(.@{class-prefix-steps}-item-custom):not(
                    .@{class-prefix-steps}-item-custom-icon
                ) {
                .@{class-prefix-steps}-item-icon {
                    width: 20px;
                }
            }
        }
        &:not(.@{class-prefix-steps}-custom) {
            .@{class-prefix-steps}-item-tail {
                left: 2.4px;
            }
        }
    }
}

.@{class-prefix-steps}-label-vertical {
    .@{class-prefix-steps}-item {
        overflow: visible;
        &-tail {
            margin-left: calc((var(--steps-title-width) - var(--steps-icon-size)) / 2);
            padding: 0 6px 0 26px;
            &::after {
                border-top: 1px solid #eeeeee;
                border-left: none;
                margin-top: 2px;
            }
        }
        &-icon {
            display: inline-block;
            margin-left: calc((var(--steps-title-width) - var(--steps-icon-size)) / 2);
        }
        &-content {
            display: block;
            width: var(--steps-title-width);
            margin-top: 12px;
            text-align: center;
        }
        &-title {
            padding-right: 0;
            &:after {
                display: none;
            }
        }
        &-main {
            display: block;
        }
    }

    &.@{class-prefix-steps}-dot {
        .@{class-prefix-steps}-item {
            &-tail {
                &::after {
                    border-top: 1px dashed #eee;
                }
            }
        }
        .@{class-prefix-steps}-item:not(.@{class-prefix-steps}-item-custom):not(
                .@{class-prefix-steps}-item-custom-icon
            ) {
            .@{class-prefix-steps}-item-tail {
                margin-left: calc((var(--steps-title-width) - var(--steps-dot-size)) / 2);
                padding: 0 10px 0 16px;
            }
            .@{class-prefix-steps}-item-icon {
                margin-left: calc((var(--steps-title-width) - var(--steps-dot-size)) / 2);
            }
        }
        &.@{class-prefix-steps}-custom {
            .@{class-prefix-steps}-item:not(.@{class-prefix-steps}-item-custom):not(
                    .@{class-prefix-steps}-item-custom-icon
                ) {
                .@{class-prefix-steps}-item-icon {
                    margin-bottom: 4px;
                }
            }
        }
    }
}

.@{class-prefix-steps}-rtl {
    .@{class-prefix-steps}-item {
        &-icon {
            margin-left: 6px;
            margin-right: 0;
        }
        &-title {
            padding-left: 10px;
            padding-right: 0;
            &::after {
                right: 100%;
                left: auto;
            }
        }
        &-tail {
            right: 0;
            left: auto;
        }
    }
}

.@{class-prefix-steps}-horizontal.@{class-prefix-steps}-rtl:not(.rbm-steps-label-vertical) {
    .@{class-prefix-steps}-item {
        margin-left: 10px;
        margin-right: 0;

        &:last-child {
            margin-left: 0;
        }
    }
}

.@{class-prefix-steps}-vertical.@{class-prefix-steps}-rtl {
    .@{class-prefix-steps}-item {
        &-icon {
            float: right;
        }
        &-tail {
            right: 9.5px;
            left: auto;
        }
        &-label {
            text-align: left;
            margin-left: 6px;
            margin-right: 0;
        }
    }

    &.@{class-prefix-steps}-dot:not(.rbm-steps-custom) {
        .@{class-prefix-steps}-item-tail {
            right: 2.4px;
            left: auto;
        }
    }

    .@{class-prefix-steps}-item-with-label {
        .@{class-prefix-steps}-item-main {
            margin-right: 46px;
            margin-left: 0;
        }
    }
}

.@{class-prefix-steps}-label-vertical.@{class-prefix-steps}-rtl {
    .@{class-prefix-steps}-item {
        &-title {
            padding-left: 0;
        }
        &-tail {
            margin-right: calc((var(--steps-title-width) - var(--steps-icon-size)) / 2);
            margin-left: 0;
            padding: 0 26px 0 6px;
        }
        &-icon {
            margin-right: calc((var(--steps-title-width) - var(--steps-icon-size)) / 2);
            margin-left: 0;
        }
    }
    &.@{class-prefix-steps}-dot {
        .@{class-prefix-steps}-item:not(.@{class-prefix-steps}-item-custom):not(
                .@{class-prefix-steps}-item-custom-icon
            ) {
            .@{class-prefix-steps}-item-tail {
                margin-right: calc((var(--steps-title-width) - var(--steps-dot-size)) / 2);
                padding: 0 16px 0 10px;
                margin-left: 0;
            }
            .@{class-prefix-steps}-item-icon {
                margin-right: calc((var(--steps-title-width) - var(--steps-dot-size)) / 2);
                margin-left: 0;
            }
        }
    }
}
