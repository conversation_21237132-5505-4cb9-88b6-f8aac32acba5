@import '../../Theme/index.less';

@class-prefix-tabs: ~'rbm-tabs';

:root {
    // Tabs组件主题变量
    --rbm-tabs-line-background: unset;
    --_rbm-tabs-line-background: linear-gradient(
        -73deg,
        var(--rbm-color-primary, #ffdd00) 0%,
        var(--rbm-color-primary, #ffdd00) 100%
    );
    --rbm-tabs-active-background: unset;
    --_rbm-tabs-active-background: linear-gradient(
        -68deg,
        var(--rbm-color-primary, #ffdd00) 0%,
        var(--rbm-color-primary, #ffdd00) 100%
    );
    --rbm-tabs-active-text-color: unset;
    --_rbm-tabs-active-text-color: var(--rbm-font-color-text, #222222);
    --rbm-tabs-content-padding: unset;
    --_rbm-tabs-content-padding: var(--rbm-space-6, 12px);
}

.tab-dot(@top, @right) {
    ::after {
        content: '';
        position: absolute;
        display: inline-block;
        right: @right;
        top: @top;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #ff192d;
    }
}

.tab-badge(@top, @right) {
    position: absolute;
    display: inline-block;
    right: @right;
    top: @top;
    border-radius: 10000px;
    background-color: #ff192d;
    font-size: 11px;
    height: 16px;
    min-width: 16px;
    line-height: 16px;
    text-align: center;
    color: #fff;
    padding: 0 4px;
}

.@{class-prefix-tabs} {
    &-header {
        position: relative;
        background-color: #fff;
    }

    &-header-transparent {
        background-color: transparent;
    }

    &-top-fix {
        position: sticky;
        top: 0;
        left: 0;
        right: 0;
        z-index: 10;
    }

    &-tab-list {
        position: relative;
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;
        align-items: center;
        position: relative;
        overflow-x: scroll;
        scrollbar-width: none;
    }

    &-tab-badge-line {
        .tab-badge(6px, -5px);
    }

    &-tab-badge-card {
        .tab-badge(-2px, -2px);
    }

    &-tab-stretch-list {
        overflow-x: none;
    }

    &-tab-list::-webkit-scrollbar {
        display: none;
    }

    &-tab-card-list,
    &-tab-card-arrow-list {
        padding: 12px 10px;
    }

    &-tab-line {
        position: absolute;
        bottom: 0;
        height: 6px;
        background: var(--rbm-tabs-line-background, var(--_rbm-tabs-line-background));
        border-radius: 2px;
    }

    &-tab {
        color: var(--secondary-color);
        position: relative;
        margin: 0 auto;
    }

    &-tab-dot-line {
        .tab-dot(14px, 10px);
    }

    &-tab-dot-card {
        .tab-dot(-2px, -2px);
    }

    &-tab-wrapper-stretch {
        flex: 1;
        width: 1%;
    }

    &-tab-line-wrapper &-tab {
        cursor: pointer;
        width: min-content;
        white-space: nowrap;
        padding: 16px 12px 8px;
        font-size: 16px;
        -webkit-tap-highlight-color: transparent;
    }

    &-tab-card-wrapper &-tab,
    &-tab-card-arrow-wrapper &-tab {
        cursor: pointer;
        padding: 10px 16px;
        width: max-content;
        font-size: 12px;
        background: #f5f6fa;
        border-radius: 4px;
        z-index: 2;
        -webkit-tap-highlight-color: transparent;
    }

    &-tab-line-wrapper &-tab-stretch,
    &-tab-card-wrapper &-tab-stretch,
    &-tab-card-arrow-wrapper &-tab-stretch {
        width: 100%;
        // overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
        white-space: nowrap;
        box-sizing: border-box;
    }

    &-tab-stretch-auto {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &-tab-card-arrow-wrapper &-tab-active::after {
        content: '';
        position: absolute;
        bottom: -4px; /* Adjust this value to move the triangle up or down */
        left: 50%;
        transform: translateX(-50%) rotate(45deg);
        width: 8px;
        height: 8px;
        background: inherit;
        border-bottom-right-radius: 2px;
        z-index: 1;
    }

    &-tab-line-wrapper &-tab-active {
        font-family: var(--font-family-base);
        font-size: 18px;
        font-weight: 500;
        color: var(--rbm-tabs-active-text-color, var(--_rbm-tabs-active-text-color));
    }

    &-tab-card-wrapper &-tab-active,
    &-tab-card-arrow-wrapper &-tab-active {
        font-family: var(--font-family-base);
        font-weight: 500;
        color: var(--rbm-tabs-active-text-color, var(--_rbm-tabs-active-text-color));
        background: var(--rbm-tabs-active-background, var(--_rbm-tabs-active-background));
    }

    &-tab-disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    &-tab-fixed-width {
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
        box-sizing: border-box;
    }

    &-content {
        padding: var(--rbm-tabs-content-padding, var(--_rbm-tabs-content-padding));
    }

    &-hide {
        display: none;
    }
}

.@{class-prefix-tabs}-rtl {
    direction: rtl;

    &.@{class-prefix-tabs}-tab-card-arrow-wrapper &.@{class-prefix-tabs}-tab-active::after {
        border-bottom-left-radius: 2px;
    }
}
