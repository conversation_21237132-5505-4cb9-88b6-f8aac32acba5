@import '../../var.less';

@class-prefix-tab-bar: ~'rbm-tab-bar';

:root {
    --rbm-tab-bar-height: 49px;
    --rbm-tab-bar-background: #ffffff;
    --rbm-tab-bar-padding: 5px 0;
    --rbm-tab-bar-item-font-size: 10px;
    --rbm-tab-bar-item-line-height: 14px;
    --rbm-tab-bar-item-text-color: #222222;
    --rbm-tab-bar-item-background: #ffffff;
    --rbm-tab-bar-item-active-background: linear-gradient(135deg, #ffe74d 1%, #ffdd1a 100%);
    --rbm-tab-bar-item-icon-size: 24px;
    --rbm-tab-bar-item-active-icon-size: 40px;
    --rbm-tab-bar-item-text-margin-top: 1px;
}

.@{class-prefix-tab-bar} {
    box-sizing: border-box;
    width: 100%;
    background-color: var(--rbm-tab-bar-background);

    &__content {
        display: flex;
        width: 100%;
        padding: var(--rbm-tab-bar-padding);
        height: var(--rbm-tab-bar-height);
    }

    &--fixed {
        position: fixed;
        bottom: 0;
        left: 0;
    }
}

.@{class-prefix-tab-bar}-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--rbm-tab-bar-item-text-color);
    font-size: var(--rbm-tab-bar-item-font-size);
    line-height: var(--rbm-tab-bar-item-line-height);
    background: var(--rbm-tab-bar-item-background);

    &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        // margin-bottom: var(--rbm-tab-bar-item-icon-margin-bottom);
        font-size: var(--rbm-tab-bar-item-icon-size);
        height: var(--rbm-tab-bar-item-icon-size);

        .@{class-prefix-tab-bar}-icon {
            display: block;
        }

        .@{class-prefix-tab-bar}-badge {
            margin-top: 4px;
        }

        img {
            display: block;
            height: 20px;
        }
    }

    &__text {
        margin-top: var(--rbm-tab-bar-item-text-margin-top);
    }

    &--active {
        .@{class-prefix-tab-bar}-item__icon {
            background: var(--rbm-tab-bar-item-active-background);
            height: var(--rbm-tab-bar-item-active-icon-size);
            width: var(--rbm-tab-bar-item-active-icon-size);
            border-radius: var(--rbm-tab-bar-item-active-icon-size);
        }
    }
}