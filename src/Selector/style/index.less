@import '../../var.less';

@class-prefix-selector: ~'rbm-selector';

:root {
    // 背景色
    --rbm-selector-background: unset;
    --_rbm-selector-background: var(--rbm-page-bg);

    --rbm-selector-active-background: unset;
    --_rbm-selector-active-background: var(--rbm-color-primary);

    // 文字颜色
    --rbm-selector-text-color: unset;
    --_rbm-selector-text-color: var(--rbm-font-color-text);

    --rbm-selector-active-text-color: unset;
    --_rbm-selector-active-text-color: var(--rbm-font-color-text);

    // 边框
    --rbm-selector-border: 0.5px solid #999999;

    --rbm-selector-active-border: 0.5px solid var(--rbm-color-warning);

    // 圆角
    --rbm-selector-border-radius: var(--rbm-radius-2);

    // 字体大小
    --rbm-selector-font-size: unset;
    --_rbm-selector-font-size: var(--rbm-font-size-s);

    // 内边距
    --rbm-selector-padding: unset;
    --_rbm-selector-padding: 9px 12px;

    // 字重
    --rbm-selector-font-weight: var(--rbm-font-weight-normal);

    --rbm-selector-active-font-weight: var(--rbm-font-weight-bold);

    // 间距
    --rbm-selector-margin-right: var(--rbm-space-4);

    --rbm-selector-margin-bottom: var(--rbm-space-4);
}

.@{class-prefix-selector} {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    &::-webkit-scrollbar {
        display: none;
    }
    &-vertical {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }

    &-item {
        flex: 0 0 auto;
        position: relative;
        opacity: 1;
        cursor: pointer;
        display: inline-block;
        text-align: center;
        line-height: 1;
        user-select: none;
        -webkit-tap-highlight-color: transparent;
        border-radius: var(--rbm-selector-border-radius);
        margin-right: var(--rbm-selector-margin-right);
        margin-bottom: var(--rbm-selector-margin-bottom);
        background-color: var(--rbm-selector-background, var(--_rbm-selector-background));
        color: var(--rbm-selector-text-color, var(--_rbm-selector-text-color));
        font-size: var(--rbm-selector-font-size, var(--_rbm-selector-font-size));
        padding: var(--rbm-selector-padding, var(--_rbm-selector-padding));
        font-weight: var(--rbm-selector-font-weight);

        &-line {
            border: var(--rbm-selector-border);
            // line模式下无背景色
            --rbm-selector-background: var(--rbm-gray-6);
        }

        &-weaken {
            --_rbm-selector-text-color: var(--rbm-font-color-sub);
        }

        &-has-icon {
            display: inline-flex;
            align-items: center;
        }

        &-description {
            font-size: 11px;
            color: var(--rbm-font-color-text);
            font-weight: 400;
            margin-top: 5px;
        }
        &-weaken &-description {
            color: var(--rbm-font-color-sub);
        }

        &-check-mark {
            min-height: 48px;
            padding: 0 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        // 各类型方框激活态样式
        &-active {
            background-color: var(
                --rbm-selector-active-background,
                var(--_rbm-selector-active-background)
            );
            color: var(--rbm-selector-active-text-color, var(--_rbm-selector-active-text-color));
        }

        &-line-active,
        &-arrow-line-active {
            position: relative;
            --_rbm-selector-active-text-color: var(--rbm-font-color-text);
            --rbm-selector-active-background: #ffe7d6;
            border: var(--rbm-selector-active-border);
            font-weight: var(--rbm-selector-active-font-weight);
        }

        &-panel-active,
        &-arrow-panel-active {
            position: relative;
            --_rbm-selector-active-text-color: var(--rbm-font-color-text);
            --_rbm-selector-active-background: var(--rbm-color-primary);
            font-weight: var(--rbm-selector-active-font-weight);
        }

        &-weaken-active,
        &-arrow-weaken-active {
            --_rbm-selector-active-background: #fff8e1;
            --_rbm-selector-active-text-color: var(--rbm-font-color-text);
            font-weight: 500;
            .@{class-prefix-selector}-item-description {
                color: var(--rbm-color-warning);
            }
        }

        // 各类型元素选中时气泡样式
        &-arrow-panel-active::after,
        &-arrow-weaken-active::after,
        &-arrow-line-active::after {
            content: '';
            position: absolute;
            bottom: -4.5px; /* Adjust this value to move the triangle up or down */
            left: 50%;
            transform: translateX(-50%) rotate(45deg);
            width: 8px;
            height: 8px;
            border-bottom-right-radius: 2px;
            border-top: none;
            border-left: none;
            z-index: 1;
            background-color: var(
                --rbm-selector-active-background,
                var(--_rbm-selector-active-background)
            );
        }

        &-arrow-panel-active::after {
            border: 1px solid
                var(--rbm-selector-active-background, var(--_rbm-selector-active-background));
        }

        &-arrow-weaken-active::after {
            border: 1px solid var(--rbm-color-warning-bg);
        }

        &-arrow-line-active::after {
            border: var(--rbm-selector-active-border);
            border-top: none;
            border-left: none;
        }

        // 背景色与边框不同，需要再加一层覆盖箭头顶部边框
        &-arrow-pointer::after {
            content: '';
            position: absolute;
            bottom: -4px;
            width: 8px;
            height: 8px;
            left: 50%;
            transform: translateX(-50%) rotate(45deg);
            background-color: var(--rbm-color-warning-disabled);
            z-index: 10;
            border-bottom-right-radius: 2px;
            border-top: none;
            border-left: none;
            z-index: 1;
        }

        &-disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        &-small {
            --_rbm-selector-font-size: var(--rbm-font-size-s);
            --_rbm-selector-padding: var(--rbm-space-3) var(--rbm-space-6);
        }

        &-large {
            --_rbm-selector-font-size: var(--rbm-font-size-md);
            --_rbm-selector-padding: 9px var(--rbm-space-6);
        }

        .@{class-prefix-selector}-check-mark-icon {
            color: var(--rbm-selector-text-color, var(--_rbm-selector-text-color));
            margin-top: 5px;
        }
    }
    .@{class-prefix-selector}-rtl {
        direction: rtl;
    }
    .@{class-prefix-selector}-direct {
        transform: rotate(180deg);
    }
}

.@{class-prefix-selector}-wrap {
    flex-wrap: wrap;
}
