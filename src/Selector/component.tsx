import React from 'react';
import classNames from 'classnames';
import { GlobalConfigContext } from '../ConfigProvider'; // 引入GlobalConfigContext
import { useFieldNames } from '../utils/use-field-names';
import { mergeProps } from '../utils/with-default-props';
import { usePropsValue } from '../utils/use-props-value';
import Icon from '../Icon';
import { SelectorOption, SelectorProps, SelectorValue } from './interface';

const classPrefix = 'rbm-selector';

const defaultProps = {
    multiple: false,
    disabled: false,
    defaultValue: [],
    options: [],
    showCheckMark: false,
    arrow: false,
    size: 'normal',
    type: 'panel',
    wrap: false,
};

function Selector<V extends SelectorValue>(p: SelectorProps<V>) {
    const props = mergeProps(defaultProps, p);
    // 获取direction
    const { direction } = React.useContext(GlobalConfigContext);
    const {
        multiple,
        disabled,
        options,
        className,
        style,
        size,
        arrow,
        type,
        showCheckMark,
        suffixIcon,
        prefixIcon,
        onChange,
        wrap,
    } = props;
    const _type = !['line', 'panel', 'weaken'].includes(type) ? 'panel' : type;
    const [labelName, valueName, descriptionName, disabledName] = useFieldNames(props.fieldNames);
    const [value, setValue] = usePropsValue({
        value: props.value,
        defaultValue: props.defaultValue,
        onChange: val => {
            onChange?.(
                val,
                options.filter(item => val.includes(item[valueName])),
            );
        },
    });

    const renderSelectItem = <V extends SelectorValue>(item: SelectorOption<V>) => {
        const active = (value || []).includes(item[valueName]);
        const _disabled = item[disabledName] || disabled;
        const itemCls = classNames(`${classPrefix}-item`, {
            [`${classPrefix}-item-${size}`]: size !== 'normal',
            [`${classPrefix}-item-check-mark`]: showCheckMark,
            [`${classPrefix}-item-disabled`]: _disabled,
            [`${classPrefix}-rtl`]: direction === 'RTL',
            [`${classPrefix}-item-has-icon`]: suffixIcon || prefixIcon,
            [`${classPrefix}-item-active`]: active,
            [`${classPrefix}-item-arrow-${_type}-active`]: arrow && active,
            [`${classPrefix}-item-${_type}-active`]: active && !arrow,
            [`${classPrefix}-item-${_type}`]: _type !== 'panel',
            [`${className}`]: className,
        });
        return (
            // eslint-disable-next-line jsx-a11y/interactive-supports-focus
            <div
                key={item[valueName]}
                className={itemCls}
                style={style}
                onClick={() => {
                    if (_disabled) {
                        return;
                    }
                    if (multiple) {
                        const val = active
                            ? value.filter(v => v !== item[valueName])
                            : [...value, item[valueName]];
                        setValue(val);
                    } else {
                        const val = active ? [] : [item[valueName]];
                        setValue(val);
                    }
                }}
                role="option"
                aria-selected={(active && !multiple) || (active && multiple)}
            >
                {prefixIcon ? (
                    <span
                        className={classNames(`${classPrefix}-item-prefix-icon`, {
                            [`${classPrefix}-direct`]: direction === 'RTL',
                        })}
                    >
                        {prefixIcon}
                    </span>
                ) : null}
                <span>{item[labelName]}</span>
                {item[descriptionName] ? (
                    <div className={`${classPrefix}-item-description`}>{item[descriptionName]}</div>
                ) : null}
                {active && showCheckMark ? (
                    <div className={`${classPrefix}-check-mark-icon`}>
                        <Icon name="check" color={type === 'weaken' ? '#ff6a00' : '#000'} />
                    </div>
                ) : null}
                {suffixIcon ? (
                    <span
                        className={classNames(`${classPrefix}-item-suffix-icon`, {
                            [`${classPrefix}-direct`]: direction === 'RTL',
                        })}
                    >
                        {suffixIcon}
                    </span>
                ) : null}
                {arrow && active ? <span className={`${classPrefix}-item-arrow-pointer`} /> : null}
            </div>
        );
    };

    const wrapperClassNames = [
        `${classPrefix}`,
        { [`${classPrefix}-vertical`]: showCheckMark },
        wrap ? `${classPrefix}-wrap` : null,
    ];

    return (
        <div role="listbox" className={classNames(...wrapperClassNames)}>
            {options.map(item => renderSelectItem(item))}
        </div>
    );
}

export default Selector;
