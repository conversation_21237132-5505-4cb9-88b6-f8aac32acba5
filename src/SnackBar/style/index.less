@class-prefix-snack-bar: ~'rbm-snack-bar';

:root {
    --rbm-snackbar-background: rgba(0, 0, 0, 0.6);
    
    --rbm-snackbar-text-color: var(--rbm-gray-6);
    
    --rbm-snackbar-border-radius: unset;
    --_rbm-snackbar-border-radius: unset;
}

.@{class-prefix-snack-bar} {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
    position: fixed;

    .@{class-prefix-snack-bar}-wrap {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;

        width: auto;
        overflow: auto;
        pointer-events: all;
        box-sizing: border-box;
        text-align: center;
        word-break: break-all;
        gap: var(--rbm-space-4);
        font-size: var(--rbm-font-size-s);
        line-height: var(--rbm-line-height-s);
        padding: var(--rbm-space-5) var(--rbm-space-6);
        background-color: var(--rbm-snackbar-background);
        border-radius: var(--rbm-snackbar-border-radius, var(--_rbm-snackbar-border-radius));
        color: var(--rbm-snackbar-text-color);
    }
    .@{class-prefix-snack-bar}-wrap-center {
        --_rbm-snackbar-border-radius: 6px;
    }
    .@{class-prefix-snack-bar}-wrap-side {
        --_rbm-snackbar-border-radius: 19px 0 0 19px;
    }

    .@{class-prefix-snack-bar}-icon-wrap {
        height: 14px;
        width: 14px;
        display: flex;
        justify-content: left;
        align-items: center;
    }

    .@{class-prefix-snack-bar}-content-wrap {
        text-align: start;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        font-size: var(--rbm-font-size-s);
        flex: 1;
    }
    &-icon-size {
        font-size: var(--rbm-font-size-xxs);
    }
}