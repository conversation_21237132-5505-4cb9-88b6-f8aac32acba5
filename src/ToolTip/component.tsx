import React, { useCallback, useState, useEffect, useMemo } from 'react';
import RCTooltip from 'rc-tooltip';
import getPlacements from './getPlacements';
import { ToolTipProps } from './interface';
import { isFragment } from '../Form/util';

const classPrefix = 'rbm-tooltip';

// 定义事件监听器选项类型
const listenerOptions: AddEventListenerOptions = { passive: true };

function Component(props: ToolTipProps) {
    const {
        defaultOpen = false,
        title,
        placement = 'top',
        open: propsOpen,
        className = '',
        overlayClassName = '',
        overlayStyle = {},
        popupStyle = {},
        rightContent,
        onOpenChange,
        arrow = true,
        children,
        color,
        zIndex = 900,
        destroyTooltipOnHide = true,
        mode = 'default',
        arrowPointAtCenter = false,
        autoAdjustOverflow = true,
        getTooltipContainer,
        hideOnScroll = false,
    } = props;
    const compatibleChildren =
        React.isValidElement(children) && !isFragment(children) ? (
            children
        ) : (
            <span>{children}</span>
        );

    const [open, setOpen] = useState(defaultOpen);

    const hideTooltip = useCallback(() => {
        if (propsOpen === undefined) {
            // 非受控模式
            setOpen(false);
        }
    }, [propsOpen]);

    // 移动端滚动时隐藏
    const handleTouchMove = useCallback(
        (e: TouchEvent) => {
            if (e.touches.length === 1) {
                requestAnimationFrame(hideTooltip);
            }
        },
        [hideTooltip],
    );

    useEffect(() => {
        if (propsOpen !== undefined) {
            setOpen(propsOpen);
        }
    }, [propsOpen]);

    useEffect(() => {
        if (!hideOnScroll) return undefined;

        window.addEventListener('touchmove', handleTouchMove, listenerOptions);
        return () => window.removeEventListener('touchmove', handleTouchMove, listenerOptions);
    }, [hideOnScroll, handleTouchMove]);

    const tooltipPlacements = useMemo(
        () => getPlacements(autoAdjustOverflow, arrowPointAtCenter),
        [autoAdjustOverflow, arrowPointAtCenter],
    );

    const controlDefaultVisible = useCallback(
        (v: boolean) => {
            if (propsOpen === undefined) {
                setOpen(v);
            }
            onOpenChange?.(v);
        },
        [onOpenChange, propsOpen],
    );

    const bgStyle = color ? { '--rbm-tooltip-bg-color': color } : {};
    return (
        <RCTooltip
            trigger={['click']}
            prefixCls={classPrefix}
            placement={placement}
            overlayClassName={`${mode === 'primary' ? 'primary' : 'default'} ${overlayClassName}`}
            overlayStyle={{ ...overlayStyle, ...bgStyle }}
            overlay={
                <div style={{ ...popupStyle }} className={`${classPrefix}-tip-box ${className}`}>
                    {title}
                    {rightContent || null}
                </div>
            }
            zIndex={zIndex}
            builtinPlacements={tooltipPlacements}
            showArrow={arrow}
            visible={open}
            onVisibleChange={controlDefaultVisible}
            destroyTooltipOnHide={destroyTooltipOnHide}
            getTooltipContainer={getTooltipContainer}
        >
            {compatibleChildren}
        </RCTooltip>
    );
}

export default Component;
