@class-prefix-tooltip: ~'rbm-tooltip';
:root {
    --rbm-tooltip-font-size: var(--rbm-font-size-s);
    --rbm-tooltip-font-color: unset;
    --_rbm-tooltip-font-color: unset;
    --rbm-tooltip-bg-color: unset;
    --_rbm-tooltip-bg-color: unset;
    --rbm-tooltip-line-height: var(--rbm-line-height-xxs);
    --rbm-tooltip-border-radius: var(--rbm-radius-2);
    --rbm-tooltip-shadow: var(--rbm-shadow-light);
    --rbm-tooltip-padding: 12px;
    --rbm-tooltip-z-index: 900;
}
.@{class-prefix-tooltip} {
    position: absolute;
    z-index: var(--rbm-tooltip-z-index);
    display: block;
    visibility: visible;
    font-size: var(--rbm-tooltip-font-size);
    line-height: var(--rbm-tooltip-line-height);
    --rbm-tooltip-arrow-width: 7px;
    --rbm-tooltip-distance: var(--rbm-tooltip-arrow-width);
    --rbm-tooltip-arrow-color: var(--rbm-tooltip-bg-color, var(--_rbm-tooltip-bg-color));
    &-hidden {
        display: none;
    }

    &-arrow {
        position: absolute;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
    }

    &-tip-box {
        padding: var(--rbm-tooltip-padding);
        color: var(--rbm-tooltip-font-color, var(--_rbm-tooltip-font-color));
        font-weight: var(--rbm-font-weight-bold);
        text-align: left;
        text-decoration: none;
        background-color: var(--rbm-tooltip-bg-color, var(--_rbm-tooltip-bg-color));
        border-radius: var(--rbm-tooltip-border-radius);
        box-shadow: var(--rbm-tooltip-shadow);
        min-height: 38px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    &.primary {
        --_rbm-tooltip-font-color: #2f2800;
        --_rbm-tooltip-bg-color: var(--rbm-color-primary);
    }
    &.default {
        --_rbm-tooltip-font-color: var(--rbm-gray-6);
        --_rbm-tooltip-bg-color: #222222;
    }
    .primary,
    .default {
        .@{class-prefix-tooltip}-arrow {
            border-bottom-color: var(--rbm-tooltip-bg-color, var(--_rbm-tooltip-bg-color));
        }
    }

    &-placement-top,
    &-placement-topLeft,
    &-placement-topRight {
        padding: var(--rbm-tooltip-arrow-width) 0 var(--rbm-tooltip-distance) 0;
    }
    &-placement-right,
    &-placement-rightTop,
    &-placement-rightBottom {
        padding: 0 var(--rbm-tooltip-arrow-width) 0 var(--rbm-tooltip-distance);
    }
    &-placement-bottom,
    &-placement-bottomLeft,
    &-placement-bottomRight {
        padding: var(--rbm-tooltip-distance) 0 var(--rbm-tooltip-arrow-width) 0;
    }
    &-placement-left,
    &-placement-leftTop,
    &-placement-leftBottom {
        padding: 0 var(--rbm-tooltip-distance) 0 var(--rbm-tooltip-arrow-width);
    }
    &-placement-top &-arrow,
    &-placement-topLeft &-arrow,
    &-placement-topRight &-arrow {
        bottom: calc(var(--rbm-tooltip-distance) - var(--rbm-tooltip-arrow-width));
        margin-left: calc(-1 * var(--rbm-tooltip-arrow-width));
        border-width: var(--rbm-tooltip-arrow-width) var(--rbm-tooltip-arrow-width) 0;
        border-top-color: var(--rbm-tooltip-arrow-color);
    }
    &-placement-top &-arrow {
        left: 50%;
    }
    &-placement-topLeft &-arrow {
        left: 15% !important;
    }
    &-placement-topRight &-arrow {
        left: 85% !important;
    }
    &-placement-right &-arrow,
    &-placement-rightTop &-arrow,
    &-placement-rightBottom &-arrow {
        left: calc(var(--rbm-tooltip-distance) - var(--rbm-tooltip-arrow-width));
        margin-top: calc(-1 * var(--rbm-tooltip-arrow-width));
        border-width: var(--rbm-tooltip-arrow-width) var(--rbm-tooltip-arrow-width)
            var(--rbm-tooltip-arrow-width) 0;
        border-right-color: var(--rbm-tooltip-arrow-color);
    }
    &-placement-right &-arrow {
        top: 50%;
    }
    &-placement-rightTop &-arrow {
        top: 15%;
        margin-top: 0;
    }
    &-placement-rightBottom &-arrow {
        bottom: 15%;
    }
    &-placement-left &-arrow,
    &-placement-leftTop &-arrow,
    &-placement-leftBottom &-arrow {
        right: calc(var(--rbm-tooltip-distance) - var(--rbm-tooltip-arrow-width));
        margin-top: calc(-1 * var(--rbm-tooltip-arrow-width));
        border-width: var(--rbm-tooltip-arrow-width) 0 var(--rbm-tooltip-arrow-width)
            var(--rbm-tooltip-arrow-width);
        border-left-color: var(--rbm-tooltip-arrow-color);
    }
    &-placement-left &-arrow {
        top: 50%;
    }
    &-placement-leftTop &-arrow {
        top: 15%;
        margin-top: 0;
    }
    &-placement-leftBottom &-arrow {
        bottom: 15%;
    }
    &-placement-bottom &-arrow,
    &-placement-bottomLeft &-arrow,
    &-placement-bottomRight &-arrow {
        top: calc(var(--rbm-tooltip-distance) - var(--rbm-tooltip-arrow-width));
        margin-left: calc(-1 * var(--rbm-tooltip-arrow-width));
        border-width: 0 var(--rbm-tooltip-arrow-width) var(--rbm-tooltip-arrow-width);
        border-bottom-color: var(--rbm-tooltip-arrow-color);
    }
    &-placement-bottomLeft &-arrow {
        left: 15% !important;
    }
    &-placement-bottomRight &-arrow {
        left: 85% !important;
    }
}
