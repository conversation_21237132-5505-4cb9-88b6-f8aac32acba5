import React, { useState } from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Avatar } from '@roo/roo-b-mobile' */
import { Avatar, Button } from '../../../src/index.ts';

const userList = ['U', 'User', 'LongUser'];
const gapList = [2, 4, 6, 8];

function Demo() {
    const [gapIndex, setGapIndex] = useState(0);
    const [userIndex, setUserIndex] = useState(0);

    const changeGap = () => {
        setGapIndex(gapIndex < gapList.length - 1 ? gapIndex + 1 : 0);
    };

    const changeUser = () => {
        setUserIndex(userIndex < userList.length - 1 ? userIndex + 1 : 0);
    };

    const user = userList[userIndex];
    const gap = gapList[gapIndex];
    return (
        <>
            <Avatar
                gap={gap}
            >
                {user}
            </Avatar>
            <Button onClick={changeGap}>changeGap</Button>
            <Button onClick={changeUser}>changeUser</Button>
        </>
    );
}

export default Demo;
