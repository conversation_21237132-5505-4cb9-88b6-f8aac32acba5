import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Avatar } from '@roo/roo-b-mobile' */
import { Avatar, Badge } from '../../../src/index.ts';

const Space = () => <span style={{ margin: 10 }} />;

function Demo() {
    const src = 'https://p0.meituan.net/travelcube/a6eb85963fe9b0b8daa04ad344a302dc107714.jpg';
    const avaCir = <Avatar src={src} />;
    const avaSqr = <Avatar src={src} shape="square" />;
    return (
        <div style={{ margin: '10px' }}>
            <Badge dot offset={[-2, 2]}>
                {avaCir}
            </Badge>
            <Space />
            <Badge count={1} offset={[1, -1]}>
                {avaSqr}
            </Badge>
            <Space />
            <Badge count={99} offset={[5.25, -1]}>
                {avaCir}
            </Badge>
            <Space />
            <Badge count="新" offset={[3.5, -1]}>
                {avaSqr}
            </Badge>
        </div>
    );
}
export default Demo;
