import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Checkbox } from '@roo/roo-b-mobile' */
import { Checkbox } from '@roo/roo-b-mobile';
import '../index.less';

function Demo() {
    return (
        <div className="select-container">
            <h3 className="title">基本用法</h3>
            <Checkbox
                style={{
                    '--_rbm-checkbox-margin-left': '12px',
                    '--rbm-checkbox-margin-right': '24px',
                    '--rbm-checkbox-size': '24px',
                    '--rbm-checkbox-icon-width': '2px',
                    '--rbm-checkbox-icon-border-radius': '8px',
                }}
            >
                多选
            </Checkbox>
            <br />
            <Checkbox
                defaultChecked
                disabled
                style={{ '--rbm-checkbox-icon-checked-disabled-color': '#ccc' }}
            >
                默认勾选
            </Checkbox>
        </div>
    );
}
export default Demo;
