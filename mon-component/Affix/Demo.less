.demo-container {
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    margin: 16px 0 0 16px;
}
.demo-item {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
}
.affix-container {
    height: 128px;
    overflow: auto;
}
.affix-item {
    padding: 80px 0;
}
.demo-content {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    align-items: start;
}
.demo-button {
    width: 200px;
}
.demo-title {
    font-size: 14px;
    font-weight: 500;
}
.custom-affix-class {
    border-radius: 8px;
    padding: 8px 0;
    display: flex;
    justify-content: center;
    align-items: center;
}
