import React, { useState, useRef } from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Avatar } from '@roo/roo-b-mobile' */
import { Affix, Button, Toast } from '../../../src/index.ts';
import '../Demo.less';

function Demo() {
    const [offsetTop, setOffsetTop] = useState(60);
    const affixRef = useRef(null);
    return (
        <div className="demo-container">
            <div className="demo-item">
                <h4 className="mb20">基本用法</h4>
                <Affix>
                    <Button>固定在视窗顶部</Button>
                </Affix>
            </div>

            <div className="demo-item">
                <h4 className="mb20">固定在距离底部40px</h4>
                <Affix offsetBottom={40}>
                    <Button>固定在底部40px</Button>
                </Affix>
            </div>

            <div className="demo-item">
                <h4 className="mb20">改变固定高度</h4>
                <Affix offsetTop={offsetTop} zIndex={2000}>
                    <Button onClick={() => setOffsetTop(offsetTop + 30)}>降低固定高度30px</Button>
                </Affix>
            </div>

            <div className="demo-item">
                <h4 className="mb20">固定状态改变回调</h4>
                <Affix
                    offsetTop={80}
                    onChange={affixed => {
                        Toast.open({ content: `固钉已${affixed ? '固定' : '解锁'}` });
                    }}
                    onPositionChange={affixed => console.log(`组件已${affixed ? '固定' : '解锁'}`)}
                >
                    <Button>固定状态发生变化时弹窗提示</Button>
                </Affix>
            </div>

            <div className="demo-item">
                <h4 className="mb20">为内容增加安全区域</h4>
                <Affix safeArea="top">
                    <Button>固定在视窗顶部，保留安全区域</Button>
                </Affix>
            </div>

            <div className="demo-item">
                <h4 className="mb20">手动触发位置刷新</h4>
                <Affix ref={affixRef} onPositionChange={() => console.log('手动触发位置刷新')}>
                    <Button onClick={() => affixRef.current.updatePosition()}>
                        固定在视窗顶部
                    </Button>
                </Affix>
            </div>

            <div className="demo-item">
                <h4 className="mb20">自定义固定容器</h4>
                <div>
                    用 target 设置需要监听其滚动事件的元素，默认为 window。 target 指定为非 window
                    容器时，可能会出现 target 外层元素滚动，固钉元素跑出滚动容器的问题。
                    这个时候可以通过传入 targetContainer 设置 target 外层的滚动元素。Affix
                    会监听该元素的滚动事件来实时更新滚钉元素的位置。 也可以在业务代码中自己监听
                    target 外层滚动元素的 scroll 事件，并调用 this.affixRef.updatePosition()
                    去更新固钉的位置。
                </div>
                <div className="affix-container" id="affix-target1">
                    <div className="affix-item">
                        <Affix
                            offsetTop={20}
                            style={{ marginBottom: 400 }}
                            target={() => document.getElementById('affix-target1')}
                            targetContainer={window}
                        >
                            <Button>固定在顶部</Button>
                        </Affix>
                    </div>
                </div>
                <div className="affix-container" id="affix-target2">
                    <div className="affix-item">
                        <Affix
                            offsetBottom={20}
                            style={{ marginTop: 400 }}
                            target={() => document.getElementById('affix-target2')}
                            targetContainer={window}
                        >
                            <Button>固定在底部</Button>
                        </Affix>
                    </div>
                </div>
            </div>
            <div className="demo-item">
                <h4 className="mb20">自定义固钉样式</h4>
                <div>
                    style/className用于设置固钉组件的样式，affixClassName/affixStyle用于设置fixed部分的样式
                </div>
                <Affix
                    offsetTop={160}
                    affixStyle={{ backgroundColor: '#A3D1FF', borderRadius: 8 }}
                    affixClassName="custom-affix-class"
                    style={{ backgroundColor: '#ffdd00', marginBottom: 400 }}
                    className="custom-affix-class"
                >
                    <Button>自定义固钉样式</Button>
                </Affix>
            </div>
            <div className="demo-item">
                <h4 className="mb20">占位</h4>
                <div style={{ height: 600 }} />
            </div>
        </div>
    );
}
export default Demo;
