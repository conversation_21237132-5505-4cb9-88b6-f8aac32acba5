import React, { useEffect, useState } from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { ConfigProvider } from '@roo/roo-b-mobile' */
import { ConfigProvider } from '../src';
/* mon:demo-show import en from '@roo/roo-b-mobile/lib/locale/lang/en-US' */
import en from '../src/locale/lang/en-US';
/* mon:demo-show import zh from '@roo/roo-b-mobile/lib/locale/lang/zh-CN' */
import zh from '../src/locale/lang/zh-CN';

// 高阶组件，用于处理国际化
function withProvider(WrappedComponent, customConfig = {}) {
    return function (props) {
        const [params, setParams] = useState<Record<string, any>>({});

        useEffect(() => {
            // 本地调试
            if (
                window.location.href.includes('localhost') ||
                window.location.href.includes('0.0.0.0')
            ) {
                const location = window.top?.location;
                const searchParams = new URLSearchParams(location?.search);
                const paramsObject = {};
                for (const [key, value] of Array.from(searchParams.entries())) {
                    paramsObject[key] = value;
                }
                setParams(paramsObject);
            } else {
                const handleMessage = event => {
                    // 处理接收到的消息
                    const message = event.data;
                    if (message.key === 'GLOBAL_SWITCH') {
                        setParams(message.value);
                    }
                };
                window.addEventListener('message', handleMessage);
                window.parent.postMessage({ status: 'ready' }, '*');
                // 清除事件监听器
                return () => {
                    window.removeEventListener('message', handleMessage);
                };
            }
        }, []);

        // 根据params.language选择语言
        const locale = params.language === 'en' ? en : zh;
        const direction = params.direction || 'ltr';

        useEffect(() => {
            ConfigProvider.config({
                locale,
                direction,
                holderRender: children => (
                    <ConfigProvider config={{ locale, direction }}>{children}</ConfigProvider>
                ),
            });
        }, [locale, direction]);

        // 将locale作为prop传递给WrappedComponent
        return (
            <ConfigProvider config={{ locale, direction, ...customConfig }}>
                <WrappedComponent {...props} />
            </ConfigProvider>
        );
    };
}

export default withProvider;
