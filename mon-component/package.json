{"devDependencies": {"json5": "2.2.1", "webpack": "5.40.0", "webpack-cli": "4.10.0", "@babel/cli": "7.12.17", "@babel/core": "7.12.3", "@babel/preset-env": "7.12.11", "postcss": "8.2.1", "postcss-cssnext": "3.1.0", "babel-loader": "8.2.2", "postcss-loader": "5.3.0", "css-loader": "5.2.4", "style-loader": "2.0.0", "html-webpack-plugin": "5.3.1", "less": "4.0.0", "less-loader": "^10.0.0", "style-resources-loader": "1.5.0", "mini-css-extract-plugin": "1.6.0", "gulp": "4.0.2", "gulp-less": "5.0.0", "gulp-sass": "5.1.0", "sass": "1.32.13", "sass-loader": "12.0.0", "webpack-dev-server": "3.11.2", "url-loader": "4.1.1", "file-loader": "6.2.0", "webpack-merge": "5.8.0", "babel-polyfill": "6.26.0", "@babel/preset-react": "^7.14.5", "@babel/plugin-transform-react-jsx": "7.14.5", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/preset-typescript": "7.14.5", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "@wmfe/eslint-config-mt": "^0.5.9", "babel-eslint": "^10.1.0", "es-check": "^7.2.1", "eslint": "^7.0.0", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-webpack": "^0.13.8", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-transform-css": "^2.0.0", "lint-staged": "^11.0.3", "npm-install-peers": "^1.2.2", "prettier": "^2.8.7", "pretty-quick": "^3.1.3", "ts-jest": "^29.1.2", "ts-loader": "4.4.2", "tsc": "^2.0.3", "tslib": "2.2.0", "typescript": "^4.5.5"}, "dependencies": {"classnames": "^2.5.1", "react": "^16.14.0", "react-dom": "^16.14.0", "lodash": "^4.17.21", "dayjs": "^1.11.10", "@react-spring/web": "^9.7.3", "@use-gesture/react": "^10.3.0", "rc-tooltip": "^6.1.3", "@floating-ui/dom": "^1.6.3", "ahooks": "^3.7.10", "dom-helpers": "5.2.1"}, "scripts": {"build": "webpack --mode=production", "serve": "webpack serve --mode=development"}}