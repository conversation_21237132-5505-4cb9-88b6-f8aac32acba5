import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
import { Radio, Icon } from '../../../src/index';
import '../index.less';

const customIcon = checked => {
    if (checked) {
        return <Icon name="check-circle-o" />;
    } else {
        return <Icon name="circle-o" />;
    }
};

function Demo() {
    return (
        <div className="radio-container">
            <div className="title">单选框</div>
            <div className="item">
                <Radio
                    checked
                    type="check"
                    value="未选"
                    className="test"
                    style={{
                        '--rbm-radio-size': '20px',
                        '--rbm-radio-border-radio': '10px',
                        '--rbm-radio-check-size': '60px',
                    }}
                >
                    未选
                </Radio>
            </div>
            <div className="item">
                <Radio
                    value="已选默认1"
                    style={{
                        '--rbm-radio-false-color': 'red',
                        '--rbm-radio-margin-left': '20px',
                        '--rbm-radio-margin-right': '20px',
                        '--rbm-radio-icon-border-width': '2px',
                        '--rbm-radio-icon-false-border-width': '2px',
                    }}
                >
                    已选默认1
                </Radio>
            </div>
            <div className="item">
                <Radio
                    value="已选禁用"
                    disabled
                    checked
                    style={{ '--rbm-radio-disabled-color': 'blue' }}
                >
                    已选禁用
                </Radio>
            </div>
        </div>
    );
}

export default Demo;
