import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Button } from '@roo/roo-b-mobile' */
import { Button } from '../../../src/index.ts';
import '../index.less';

const Space = () => <span style={{ margin: '4px' }} />;

const imgUrl =
    'https://s3plus.meituan.net/v1/mss_e2fc5719a5b64fa4b3686b72e677a48e/inode-fs/dist/img/csr/join-b-00a69ad1bf.png';
function Demo() {
    return (
        <div className="button-container">
            <Button href={imgUrl} target="_self">
                在相同窗口打开
            </Button>
            <Space />
            <Button href={imgUrl} target="_blank">
                在新窗口打开
            </Button>
            <Space />
            <Button href={imgUrl} target="_parent">
                在父框架中打开
            </Button>
            <Space />
            <Button href={imgUrl} target="_top">
                在整个窗口中打开
            </Button>
        </div>
    );
}
export default Demo;
