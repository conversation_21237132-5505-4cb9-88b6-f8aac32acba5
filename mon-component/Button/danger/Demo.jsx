import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Button, Icon } from '@roo/roo-b-mobile' */
import { Button, Icon } from '../../../src/index.ts';
import '../index.less';

const Space = () => <span style={{ margin: '4px' }} />;

function Demo() {
    return (
        <div className="button-container">
            <div className="item">
                <div className="header">危险按钮</div>
                <Button icon={<Icon name="search" />} danger>
                    danger default
                </Button>
                <Space />
                <Button icon={<Icon name="search" />} type="primary" danger>
                    danger primary
                </Button>
                <Space />
                <Button icon={<Icon name="search" />} type="text" danger>
                    danger text
                </Button>
            </div>

            <div className="item">
                <div className="header">危险按钮禁用</div>
                <Button icon={<Icon name="search" />} danger disabled>
                    danger default
                </Button>
                <Space />
                <Button icon={<Icon name="search" />} type="primary" danger disabled>
                    danger primary
                </Button>
                <Space />
                <Button icon={<Icon name="search" />} type="text" danger disabled>
                    danger text
                </Button>
            </div>
        </div>
    );
}
export default Demo;
