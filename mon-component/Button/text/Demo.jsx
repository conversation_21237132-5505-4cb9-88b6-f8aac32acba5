import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
import { Button } from '@roo/roo-b-mobile';
import '../index.less';

function Demo() {
    return (
        <div className="button-container" style={{ fontSize: '14px' }}>
            这是一段文字描述文字，你可以通过
            <Button
                type="text"
                size="large"
                wrapperPadding={0}
                onClick={() => {
                    window.open(
                        'https://atomic.sankuai.com/#/v2/materials/teams/213/home/<USER>/110/overview',
                    );
                }}
            >
                点击这里
            </Button>
            进入roo-b-mobile组件库官网。 也可以
            <Button
                type="text"
                wrapperPadding={0}
                style={{ color: '#acacac' }}
                onClick={() => {
                    window.open('https://roo.sankuai.com/');
                }}
            >
                点击这里
            </Button>
            进入roo官网。 同时这里还可以
            <Button type="text" wrapperPadding={0} style={{ color: 'yellow' }}>
                自定义文字
            </Button>{' '}
            颜色的链接按钮。
        </div>
    );
}
export default Demo;
