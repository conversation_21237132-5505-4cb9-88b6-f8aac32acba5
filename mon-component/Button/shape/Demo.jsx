import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
import { Button, Icon } from '@roo/roo-b-mobile';
import '../index.less';

const Space = () => <span style={{ margin: '8px' }} />;

function Demo() {
    return (
        <div className="button-container">
            <div className="header">从左至右按钮的形状分别为 circle, default, round, square</div>
            <Button shape="circle" icon={<Icon name="search" />} />
            <Space />
            <Button shape="default" icon={<Icon name="search" />} />
            <Space />
            <Button shape="round" icon={<Icon name="search" />} />
            <Space />
            <Button shape="square" icon={<Icon name="search" />} />
            <Space />
            <Button icon={<Icon name="search" />}>默认形状为round</Button>
        </div>
    );
}
export default Demo;
