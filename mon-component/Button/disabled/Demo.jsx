import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
import { Button } from '@roo/roo-b-mobile';
import '../index.less';

function Demo() {
    return (
        <div className="button-container">

            <div className="item">
                <div className="header">按钮禁用</div>
                <Button disabled>disabled</Button>
            </div>
            <div className="item">
                <div className="header">按钮禁用，仍可点击</div>
                <Button
                    disabled
                    onClickDisabled={e => {
                        console.log('禁用后的点击', e);
                    }}
                >
                    disabled-onClick
                </Button>
            </div>
        </div>
    );
}
export default Demo;
