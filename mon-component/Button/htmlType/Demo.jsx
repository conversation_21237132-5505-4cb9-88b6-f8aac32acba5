import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Button, Form, Input } from '@roo/roo-b-mobile' */
import { Button, Form, Input } from '../../../src/index.ts';
import '../index.less';

function Demo() {
    const [form] = Form.useForm();

    const handleFinish = values => {
        console.log('Success', values);
    };

    const handleReset = () => {
        form.resetFields();
    };

    return (
        <div className="button-container">
            <Form
                form={form}
                layout="vertical"
                initialValues={{ name: '' }}
                onFinish={handleFinish}
                onValuesChange={changedValues => {
                    console.log('字段值更新了', changedValues);
                }}
            >
                <Form.Item label="姓名" name="name" layout="horizontal">
                    <Input />
                </Form.Item>

                <Form.Item className="btn-wrap">
                    <Button htmlType="submit">提交</Button>
                    <Button htmlType="button" onClick={handleReset}>
                        重置
                    </Button>
                </Form.Item>
            </Form>
        </div>
    );
}
export default Demo;
