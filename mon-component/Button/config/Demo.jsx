import React, { useState } from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
import { Button, Checkbox, Icon } from '@roo/roo-b-mobile';
import '../index.less';

const options = [
    {
        label: '添加icon',
        value: { icon: <Icon name="loading" /> },
    },
    {
        label: '文字按钮',
        value: { type: 'text' },
    },
    {
        label: '圆形按钮',
        value: { shape: 'circle' },
    },
    {
        label: '禁用按钮',
        value: { disabled: true },
    },
    {
        label: 'block',
        value: { block: true },
    },
    {
        label: 'mini按钮',
        value: { size: 'mini' },
    },
    {
        label: '跳转按钮',
        value: {
            href: 'https://atomic.sankuai.com/#/v2/materials/teams/213/home/<USER>/97/component/1773?teamName=null',
            target: '_blank',
        },
    },
    {
        label: 'danger按钮',
        value: { danger: true },
    },
    {
        label: 'loading按钮',
        value: { loading: true, loadingText: '加载中1' },
    },
    {
        label: 'loading 定义icon按钮',
        value: { loading: true, loadingText: '加载中', loadingIcon: <Icon name="double-left" /> },
    },
    {
        label: 'primary outline按钮',
        value: { fill: 'outline', type: 'primary' },
    },
    {
        label: 'primary solid按钮',
        value: { fill: 'solid', type: 'primary' },
    },
    {
        label: 'primary none按钮',
        value: { fill: 'none', type: 'primary' },
    },
    {
        label: 'default none按钮',
        value: { fill: 'none', type: 'default' },
    },
    {
        label: 'default outline按钮',
        value: { fill: 'outline', type: 'default' },
    },
];

function Demo() {
    const [config, setConfig] = useState({});
    const [values, setValues] = useState([]);

    const handleValues = values =>
        values.reduce((target, value) => {
            const item = options.find(option => option.label === value)?.value || {};
            return {
                ...target,
                ...item,
            };
        }, {});

    const handleChange = values => {
        setConfig(handleValues(values));
        setValues(values);
    };

    return (
        <div className="button-container">
            <div className="title">配置Button属性：</div>
            <Checkbox.Group value={values} onChange={handleChange}>
                {options.map(({ label }) => (
                    <Checkbox key={label} value={label} className="checkbox">
                        {label}
                    </Checkbox>
                ))}
            </Checkbox.Group>
            <br />
            <Button {...config}>配置按钮</Button>
        </div>
    );
}
export default Demo;
