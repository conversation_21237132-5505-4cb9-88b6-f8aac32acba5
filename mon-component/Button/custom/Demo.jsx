import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
import { Button } from '@roo/roo-b-mobile';
import '../index.less';

function Demo() {
    /**
     * @description
     * 原API属性设置自定义样式是classNames和styles，统一使用style和className。
     * 保留原classNames和styles，但是style和className优先级高于原classNames和styles。
     */
    return (
        <div className="button-container">
            <Button
                classNames="fontColorWhite"
                className="fontColorRed"
                styles={{ background: '#198cff', boxShadow: 'none' }}
                style={{
                    background: '#ffdd00',
                }}
            >
                自定义按钮
            </Button>
        </div>
    );
}
export default Demo;
