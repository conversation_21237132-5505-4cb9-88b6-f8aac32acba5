import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
import { Button, Icon } from '@roo/roo-b-mobile';
import '../index.less';

const Space = () => <span style={{ margin: '4px' }} />;

function Demo() {
    return (
        <div className="button-container">
            <div className="header">提供了六种按钮大小</div>
            <Button icon={<Icon name="search" />} size="max">
                max
            </Button>
            <Space />
            <Button icon={<Icon name="search" />} size="large">
                large
            </Button>
            <Space />
            <Button icon={<Icon name="search" />} size="normal">
                normal
            </Button>
            <Space />
            <Button icon={<Icon name="search" />} size="middle">
                middle
            </Button>
            <Space />
            <Button icon={<Icon name="search" />} size="small">
                small
            </Button>
            <Space />
            <Button icon={<Icon name="search" />} size="mini">
                mini
            </Button>
        </div>
    );
}
export default Demo;
