import React, { useState } from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { ActionSheet, Button } from '@roo/roo-b-mobile' */
import { ActionSheet, Button, Toast } from '../../../src/index.ts';

function Demo() {
    const [basicActionSheetVisible, setBasicActionSheetVisibility] = useState(false);

    return (
        <div
            style={{
                padding:'16px 12px'
            }}
        >
            <Button onClick={() => setBasicActionSheetVisibility(true)}>测试各种 callback</Button>
            <ActionSheet
                visible={basicActionSheetVisible}
                extra="自定义 title"
                onClose={() => {
                    setBasicActionSheetVisibility(false);
                    console.log('onClose callback');
                }}
                onAction={(action, index) => {
                    console.log('action', action);
                    Toast.open({
                        content: `点击了第${index}个选项，文案是"${action.text}"`,
                        position: 'center',
                    });
                }}
                onMaskClick={() => console.log('onMaskClick callback')}
                closeOnAction
                destroyOnClose
                actions={[
                    {
                        text: '选项一',
                        description: '这是选项一的备注文字',
                        key: 1,
                        onClick: item => console.log(item.text),
                    },
                    { text: '选项二（禁用了）', key: 2, disabled: true },
                    { text: '选项三', key: 3 },
                ]}
            />
        </div>
    );
}
export default Demo;
