import React, { useState } from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { ActionSheet, Button } from '@roo/roo-b-mobile' */
import { ActionSheet, Button } from '@roo/roo-b-mobile';
import './style.less';
import { Toast } from '../../../src';

function Demo() {
    const [basicActionSheetVisible, setBasicActionSheetVisibility] = useState(false);
    const [actionsheetWithoutTitleVisible, setActionsheetWithoutTitleVisibility] = useState(false);
    const [customActionSheetVisible, setCustomActionSheetVisibility] = useState(false);
    return (
        <div
            style={{
                padding:'16px 12px'
            }}
        >
            <Button onClick={() => setBasicActionSheetVisibility(true)}>基础用法</Button>
            <ActionSheet
                visible={basicActionSheetVisible}
                extra="自定义 title"
                onClose={() => setBasicActionSheetVisibility(false)}
                onAction={(action, index) => {
                    console.log('action', action);
                    Toast.open({
                        content: `点击了第${index}个选项，文案是"${action.text}"`,
                        position: 'center',
                    });
                }}
                closeOnAction
                actions={[
                    {
                        text: '选项一',
                        description: '这是选项一的备注文字',
                        key: 1,
                        onClick: item => console.log(item.text),
                    },
                    { text: '选项二（禁用了）', key: 2, disabled: true },
                    { text: '选项三', key: 3 },
                ]}
            />
            <div id="parent" style={{ marginTop: '12px' }}>
                <Button onClick={() => setActionsheetWithoutTitleVisibility(true)}>
                    基础用法(无标题)
                </Button>
                <ActionSheet
                    getContainer={() => document.getElementById('parent')}
                    visible={actionsheetWithoutTitleVisible}
                    onClose={() => setActionsheetWithoutTitleVisibility(false)}
                    onAction={(action, index) =>
                        Toast.open({
                            content: `点击了第${index}个选项，文案是"${action.text}"`,
                            position: 'center',
                        })
                    }
                    closeOnAction
                    actions={[
                        { text: '选项1', key: 1 },
                        { text: '选项2（被禁用）', key: 2, disabled: true },
                        { text: '选项3', key: 3 },
                        { text: '选项4', key: 4 },
                    ]}
                />
            </div>
            <div style={{ marginTop: '12px' }}>
                <Button onClick={() => setCustomActionSheetVisibility(true)}>自定义样式</Button>
                <ActionSheet
                    visible={customActionSheetVisible}
                    cancelText="关闭"
                    closeOnMaskClick={false}
                    onClose={() => setCustomActionSheetVisibility(false)}
                    popupClassName="custom-popup-class"
                    styles={{
                        mask: { backgroundColor: '#F8F8F8' },
                    }}
                    onAction={(action, index) => {
                        console.log('action', action);
                        Toast.open({
                            content: `点击了第${index}个选项，文案是"${action.text}"`,
                            position: 'center',
                        });
                    }}
                    actions={[
                        { text: '自定义选项1', key: 1 },
                        { text: '自定义选项2', key: 2 },
                    ]}
                />
            </div>
        </div>
    );
}
export default Demo;
