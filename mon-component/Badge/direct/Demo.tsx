import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Badge } from '@roo/roo-b-mobile' */
import { Badge } from '../../../src/index';
import '../index.less';

function Demo() {
    const Child = () => (
        <div style={{ width: 40, height: 40, background: '#f2f3f5', borderRadius: 4 }} />
    );
    const Space = () => <span style={{ margin: 10 }} />;

    return (
        <div className="badge-container">
            <div className="title">指向型角标</div>
            <Badge count={1} isDirect>
                <Child />
            </Badge>
            <Space />
            <Badge count={100} isDirect>
                <Child />
            </Badge>
            <Space />
            <Badge count="爆品" isDirect>
                <Child />
            </Badge>
            <br />
            <br />
            <div className="title">独立展示</div>
            <Space />
            <Badge count={1} isDirect className="single" />
            <Badge count={100} isDirect className="single" />
            <Badge count="热" isDirect className="single" />
            <br />
            <br />
            <div className="title">自定义颜色、偏移量和样式</div>
            <Badge count={1} isDirect color="#198cff" offset={[5, -5]}>
                <Child />
            </Badge>
            <Space />
            <Badge count="爆品" isDirect color="#00bf7f" offset={[-10, 10]}>
                <Child />
            </Badge>
            <Space />
            <Badge count="新" isDirect className="cust-wrap">
                <Child />
            </Badge>
            <Space />
            <Badge count={10} isDirect className="cust" style={{ background: '#ffdd00' }} />
            <br />
            <br />
        </div>
    );
}
export default Demo;
