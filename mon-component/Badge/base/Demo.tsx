import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Badge, Icon } from '@roo/roo-b-mobile' */
import { Badge, Icon } from '../../../src/index';
import '../index.less';

function Demo() {
    const Child = () => (
        <div style={{ width: 40, height: 40, background: '#f2f3f5', borderRadius: 4 }} />
    );
    const Space = () => <span style={{ margin: 10 }} />;
    const iconStyle: React.CSSProperties = { position: 'relative', top: 0.8 };

    return (
        <div className="badge-container">
            <div className="title">基础用法</div>
            <Badge count={1}>
                <Child />
            </Badge>
            <Space />
            <Badge count={13}>
                <Child />
            </Badge>
            <Space />
            <Badge count="新">
                <Child />
            </Badge>
            <Space />
            <Badge count="爆品">
                <Child />
            </Badge>
            <Space />
            <Badge dot>
                <Child />
            </Badge>
            <br />
            <br />

            <div className="title">封顶数字</div>
            <Badge count={11} overflowCount={10}>
                <Child />
            </Badge>
            <Space />
            <Badge count={100}>
                <Child />
            </Badge>
            <Space />
            <Badge count="300" overflowCount={200}>
                <Child />
            </Badge>
            <br />
            <br />

            <div className="title">独立展示</div>
            <Space />
            <Badge count="20" offset={[40, 20]} className="single" />
            <Badge count="1000" overflowCount={999} className="single" />
            <Badge dot className="single" />
            <br />
            <br />

            <div className="title">自定义颜色、偏移量和样式</div>
            <Badge count={1} color="#198cff" offset={[5, -5]}>
                <Child />
            </Badge>
            <Space />
            <Badge dot color="#00bf7f" offset={[-10, 10]}>
                <Child />
            </Badge>
            <Space />
            <Badge count="新" className="cust-wrap">
                <Child />
            </Badge>
            <Space />
            <Badge count={10} className="cust" style={{ background: '#ffdd00' }} />
            <br />
            <br />

            <div className="title">自定义角标内容</div>
            <Badge count={<Icon name="question-o" color="white" style={iconStyle} />}>
                <Child />
            </Badge>
            <Space />
            <Badge count={<Icon name="info-circle-o" color="white" style={{ ...iconStyle, top: 0.6 }} />}>
                <Child />
            </Badge>
            <Space />
            <Badge count={<Icon name="warning" color="white" style={iconStyle} />}>
                <Child />
            </Badge>
            <br />
            <br />
        </div>
    );
}
export default Demo;
