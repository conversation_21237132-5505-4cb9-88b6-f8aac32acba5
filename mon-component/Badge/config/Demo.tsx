import React, { useState } from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Badge, Checkbox, Input } from '@roo/roo-b-mobile' */
import { Badge, Checkbox, Input } from '../../../src/index';
import '../index.less';

const CheckboxGroup = Checkbox.Group;

const configs = [
    {
        label: '展示0',
        value: { showZero: true },
        key: '1',
    },
    {
        label: '圆点展示',
        value: { dot: true },
        key: '2',
    },
    {
        label: '指向型',
        value: { isDirect: true },
        key: '3',
    },
    {
        label: '99封顶',
        value: { overflowCount: 99 },
        key: '4',
    },
];

const handleValue = values => {
    const result = values.reduce(
        (target, value) => {
            const item = configs.find(option => option.key === value)?.value || {};
            const obj = Object.keys(item).reduce(
                (object, key) => ({
                    ...object,
                    // eslint-disable-next-line no-nested-ternary
                    [key]: Array.isArray(target[key]) ? target[key].concat(item[key]) : item[key],
                }),
                {},
            );
            return {
                ...target,
                ...obj,
            };
        },
        { overflowCount: false },
    );
    return result;
};

function Demo() {
    const [count, setCount] = useState(1);
    const [value, setValue] = useState(['4']);
    const [config, setConfig] = useState<Record<string, any>>();

    const handleChange = val => {
        setConfig(handleValue(val));
        setValue(val);
    };

    const Child = () => (
        <div style={{ width: 40, height: 40, background: '#f2f3f5', borderRadius: 4 }} />
    );

    return (
        <div className="badge-container">
            <div className="title">配置Badge属性：</div>
            <div className="configs">
                <CheckboxGroup value={value} onChange={handleChange}>
                    {configs.map(({ key, label }) => (
                        <Checkbox key={label} value={key} className="checkbox">
                            {label}
                        </Checkbox>
                    ))}
                </CheckboxGroup>
                <Input
                    className="count-input"
                    type="number"
                    value={`${count}`}
                    onChange={value => {
                        setCount(Number(value));
                    }}
                />
            </div>
            <br />
            <Badge count={count} {...config}>
                <Child />
            </Badge>
        </div>
    );
}
export default Demo;
