/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import dayjs from 'dayjs';
import isoWeekPlugin from 'dayjs/plugin/isoWeek';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { CalendarPicker } from '@roo/roo-b-mobile' */
import { CalendarPicker, Button } from '../../../src/index';
import '../index.less';

dayjs.extend(isoWeekPlugin);

const { useState } = React;

function Demo() {
    const [singleDate, setSingleDate] = useState(null);
    const [singleDateWithLimitation, setSingleDateWithLimitation] = useState(null);

    const [demo3Open, setDemo3Open] = useState(false);
    const [demo4Open, setDemo4Open] = useState(false);

    const [demo8Open, setDemo8Open] = useState(false);

    const [demo13Open, setDemo13Open] = useState(false);
    return (
        <div className="calendar-picker-dome">
            <h3>自定义配置</h3>
            <p style={{ margin: '12px 0' }}>
                选择结果：{singleDate ? singleDate.format('YYYY-MM-DD') : '请选择'}
            </p>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
                <Button styles={{ margin: '6px' }} onClick={() => setDemo3Open(true)}>
                    打开单月选择日期弹框（无 title）
                </Button>
                <Button styles={{ margin: '6px' }} onClick={() => setDemo4Open(true)}>
                    打开单月选择日期弹框（自定义渲染）
                </Button>
                <Button styles={{ margin: '6px' }} onClick={() => setDemo13Open(true)}>
                    打开单月选择日期弹框（自定义每周以周三开始）
                </Button>
                <div>
                    <h3>日期选择(限定最近前后 3 个月工作日)</h3>
                    <p style={{ margin: '12px 0' }}>
                        选择结果：
                        {singleDateWithLimitation
                            ? singleDateWithLimitation.format('YYYY-MM-DD')
                            : '请选择'}
                    </p>
                </div>
                <Button styles={{ margin: '6px' }} onClick={() => setDemo8Open(true)}>
                    日期选择(限定最近前后 3 个月工作日)
                </Button>
            </div>
            <CalendarPicker
                open={demo3Open}
                range={false}
                value={singleDate}
                onChange={v => {
                    setSingleDate(v);
                    setDemo3Open(false);
                }}
                onCancel={() => setDemo3Open(false)}
                title={null}
            />
            <CalendarPicker
                open={demo4Open}
                range={false}
                value={singleDate}
                onChange={v => {
                    setSingleDate(v);
                    setDemo4Open(false);
                }}
                onCancel={() => setDemo4Open(false)}
                cellRender={day => (
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignContent: 'center',
                            textAlign: 'center',
                        }}
                    >
                        <div style={{ fontSize: 10 }}>&yen;400</div>
                        <div>{day.format('DD')}</div>
                        <div style={{ fontSize: 10 }}>&yen;400</div>
                    </div>
                )}
            />

            <CalendarPicker
                open={demo13Open}
                range={false}
                value={singleDate}
                weekStartsOn={3}
                onChange={v => {
                    setSingleDate(v);
                    setDemo13Open(false);
                }}
                onCancel={() => setDemo13Open(false)}
            />

            <CalendarPicker
                open={demo8Open}
                isScroll
                range={false}
                value={singleDateWithLimitation}
                minDate={dayjs().subtract(3, 'month')}
                maxDate={dayjs().add(3, 'month')}
                disabledDate={date => date.day() === 0 || date.day() === 6}
                onChange={v => {
                    setSingleDateWithLimitation(v);
                    setDemo8Open(false);
                }}
                onCancel={() => setDemo8Open(false)}
            />

            {/* demo内容 */}
        </div>
    );
}
export default Demo;
