/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import isoWeekPlugin from 'dayjs/plugin/isoWeek';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { CalendarPicker } from '@roo/roo-b-mobile' */
import { CalendarPicker, Button, Checkbox } from '../../../src/index';
import '../index.less';

dayjs.extend(isoWeekPlugin);

const { useState } = React;
const CheckboxGroup = Checkbox.Group;
const options = [
    {
        label: '是否滚动展示模式',
        value: 'isScroll',
    },
    {
        label: '是否需要确认按钮',
        value: 'needConfirm',
    },
    {
        label: '是否开启多选',
        value: 'multiple',
    },
    {
        label: '多选时，是否自动排序',
        value: 'order',
    },
    {
        label: '是否时间段选择',
        value: 'range',
    },
];
type dateType = Dayjs | Dayjs[] | [Dayjs, Dayjs] | null;

function Demo() {
    const [singleDate, setSingleDate] = useState<dateType>(null);
    const [demo20Open, setDemo20Open] = useState(false);
    const [checkboxValue, setCheckboxValue] = useState<React.ReactText[]>([]);
    const chooseApi = () => {
        const apiMap = {};
        checkboxValue.forEach(i => {
            apiMap[i] = true;
        });
        return apiMap;
    };

    const date = () => {
        let msg = '请选择';
        if (checkboxValue.includes('range')) {
            if (singleDate && Array.isArray(singleDate) && singleDate.every(d => d !== null)) {
                msg = singleDate.map(d => d.format('YYYY-MM-DD')).join(' - ');
            }
        } else if (checkboxValue.includes('multiple')) {
            if (singleDate && Array.isArray(singleDate) && singleDate.length > 0) {
                msg = singleDate.map(d => d.format('YYYY-MM-DD')).join(', ');
            }
        } else {
            if (singleDate && !Array.isArray(singleDate)) {
                msg = singleDate.format('YYYY-MM-DD');
            }
        }
        return msg;
    };

    return (
        <div className="calendar-picker-dome">
            <h3>主要功能</h3>
            <p style={{ margin: '12px 0' }}>选择结果：{date()}</p>
            <CheckboxGroup
                value={checkboxValue}
                onChange={value => {
                    setSingleDate(null);
                    setCheckboxValue(value);
                }}
            >
                {options.map(i => (
                    <Checkbox key={i.value} value={i.value}>{i.label}</Checkbox>
                ))}
            </CheckboxGroup>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
                <Button styles={{ margin: '6px' }} onClick={() => setDemo20Open(true)}>
                    打开选择日期弹框
                </Button>
            </div>
            {/* @ts-ignore */}
            <CalendarPicker
                open={demo20Open}
                value={singleDate}
                order={false}
                onChange={v => {
                    setSingleDate(v);
                    setDemo20Open(false);
                }}
                onCancel={() => setDemo20Open(false)}
                {...chooseApi()}
            />

            {/* demo内容 */}
        </div>
    );
}
export default Demo;
