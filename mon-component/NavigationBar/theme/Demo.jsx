import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { NavigationBar } from '@roo/roo-b-mobile' */
// import KNB from '@dp/knb';
import { NavigationBar, Dialog, ConfigProvider } from '@roo/roo-b-mobile';

function DemoBlock(props) {
    return <div style={{ padding: '10px 0', background: '#F5F6FA' }}>{props.widget}</div>;
}

function Demo() {
    const backCheck = () =>
        new Promise(res => {
            // eslint-disable-next-line no-alert
            Dialog.confirm({ content: '是否退出页面', onOk: () => res() });
        });
    return (
        <div>
            <DemoBlock
                widget={
                    <NavigationBar
                        fixed={false}
                        onBack={backCheck}
                        title="基础使用"
                        style={{
                            '--rbm-navigationBar-padding-right': '200px',
                            '--rbm-navigationBar-padding-left': '100px',
                            '--rbm-navigationBar-back-padding': '40px',
                            '--rbm-navigationBar-back-max-width': '10px',
                            '--_rbm-navigationBar-title-padding': '20px',
                            '--rbm-navigationBar-action-max-width': '100px',
                        }}
                        right={<div>123</div>}
                    />
                }
            />
            <ConfigProvider config={{ direction: 'RTL' }}>
                <DemoBlock
                    widget={
                        <NavigationBar
                            fixed={false}
                            style={{
                                '--rbm-navigationBar-padding-right-rtl': '100px',
                                '--rbm-navigationBar-padding-left-rtl': '100px',
                            }}
                            onBack={backCheck}
                            title="退出页面前询问"
                        />
                    }
                />
            </ConfigProvider>
        </div>
    );
}

export default Demo;
