import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { Swiper } from '@roo/roo-b-mobile' */
import { Swiper, Toast } from '@roo/roo-b-mobile';
import '../index.less';

const colors = ['#A3D1FF', '#99E5CC', '#FFC399', '#FFA3AB'];

const items = colors.map((color, index) => (
    <Swiper.Item key={index}>
        <div
            style={{
                background: color,
                display: 'flex',
                height: '100%',
                justifyContent: 'center',
                alignItems: 'center',
            }}
            onClick={() => {
                Toast.open({ content: `你点击了卡片 ${index + 1}` });
            }}
        >
            {index + 1}
        </div>
    </Swiper.Item>
));

function Demo() {
    return (
        <>
            <div style={{ width: '100%' }}>
                <p style={{ margin: '16px 12px 0' }}>基础用法</p>
                <Swiper
                    className="swiper-test"
                    defaultIndex={1}
                    style={{
                        '--rbm-swiper-border-radius': '20px',
                        '--rbm-swiper-height': '500px',
                        '--rbm-swiper-width': '50%',
                        '--rbm-swiper-track-padding': '40px',
                        '--rbm-swiper-slide-size': '100%',
                        '--rbm-swiper-track-offset': '100px',
                    }}
                >
                    {items}
                </Swiper>
            </div>
        </>
    );
}
export default Demo;
