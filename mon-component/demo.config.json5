[
    {
        key: 'Input',
        children: [
            {
                key: 'theme',
                title: '主题定制',
                description: '自定义主题',
                entry: './Input/theme/index',
                displayCode: './Input/theme/Demo',
            },
            {
                key: 'virtualList',
                title: 'virtualList使用',
                description: 'virtualized字段选择实现虚拟列表',
                entry: './List/virtualList/index',
                displayCode: './List/virtualList/Demo',
            },
            {
                key: 'scrollTopList',
                title: '提供api让list滚动到顶部',
                description: '提供api让list滚动到顶部',
                entry: './List/home/<USER>',
                displayCode: './List/home/<USER>',
            },
        ],
    },
    {
        key: 'Radio',
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: '按钮支持checked,defaultChecked,onChange,disabled,type,icon,labelPosition',
                entry: './Radio/base/index',
                displayCode: './Radio/base/Demo',
            },
            {
                key: 'group',
                title: 'group用法',
                description: '生成一组radio',
                entry: './Radio/group/index',
                displayCode: './Radio/group/Demo',
            },
            {
                key: 'config',
                title: '主要功能',
                description: '勾选对应配置项，RadioGroup可配置disabled、type、icon、direction等。',
                entry: './Radio/config/index',
                displayCode: './Radio/config/Demo',
            },
            {
                key: 'theme',
                title: '主题定制',
                description: 'radio主题定制',
                entry: './Radio/theme/index',
                displayCode: './Radio/theme/Demo',
            },
        ],
    },
    {
        key: 'Input',
        children: [
            {
                key: 'demoSample',
                title: '基本用法',
                description: '',
                entry: './Input/demoSample/index',
                displayCode: './Input/demoSample/Demo',
            },
            {
                key: 'Custom',
                title: '插入内容',
                description: '可以通过 prefix suffix 可以插入内容。',
                entry: './Input/custom/index',
                displayCode: './Input/custom/Demo',
            },
            {
                key: 'Textarea',
                title: '多行输入',
                description: 'Input.TextArea 可用于多行输入， autoSize 可以使文本域自适应高度，还能设置其最小和最大高度。',
                entry: './Input/textarea/index',
                displayCode: './Input/textarea/Demo',
            },
            {
                key: 'Search',
                title: '搜索场景的输入框组件',
                description: 'Input.Search 比 Input多了个onSearch属性',
                entry: './Input/search/index',
                displayCode: './Input/search/Demo',
            },
            {
                key: 'event',
                title: '事件示例',
                description: '',
                entry: './Input/event/index',
                displayCode: './Input/event/Demo',
            },
            {
                key: 'methods',
                title: '方法使用示例',
                description: '可以使用focus、blur等方法控制输入框',
                entry: './Input/methods/index',
                displayCode: './Input/methods/Demo',
            },
        ],
    },
    {
        key: 'Tag',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: '标签的基础用法。',
                entry: './Tag/base/index',
                displayCode: './Tag/base/Demo',
            },
            {
                key: 'Config',
                title: '配置示例',
                description: '根据选择的配置项动态设置标签属性。',
                entry: './Tag/config/index',
                displayCode: './Tag/config/Demo',
            },
        ],
    },
    {
        key: 'DatePicker',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: '时间选择器基础用法',
                entry: './DatePicker/base/index',
                displayCode: './DatePicker/base/Demo',
            },
            {
                key: 'MinMax',
                title: '日期限定范围',
                description: '限制最大最小时间',
                entry: './DatePicker/minMax/index',
                displayCode: './DatePicker/minMax/Demo',
            },
            {
                key: 'FilterItem',
                title: '过滤选项',
                description: '过滤时间选项',
                entry: './DatePicker/filterItem/index',
                displayCode: './DatePicker/filterItem/Demo',
            },
            {
                key: 'RenderLabel',
                title: '自定义渲染标签',
                description: '自定义时间选项的具体格式',
                entry: './DatePicker/renderLabel/index',
                displayCode: './DatePicker/renderLabel/Demo',
            },
            {
                key: 'Customize',
                title: '自定义样式&文案',
                description: '自定义样式&文案',
                entry: './DatePicker/customize/index',
                displayCode: './DatePicker/customize/Demo',
            },
            {
                key: 'Use12Hour',
                title: '12小时制',
                description: '12小时制时间筛选',
                entry: './DatePicker/use12Hour/index',
                displayCode: './DatePicker/use12Hour/Demo',
            },
            {
                key: 'Loading',
                title: '加载状态',
                description: '自定义加载状态及内容',
                entry: './DatePicker/loading/index',
                displayCode: './DatePicker/loading/Demo',
            },
            {
                key: 'Children',
                title: '受控模式',
                description: '受控模式',
                entry: './DatePicker/children/index',
                displayCode: './DatePicker/children/Demo',
            },
            {
                key: 'maskClosable',
                title: '禁止点击遮罩层关闭',
                description: '禁止点击遮罩层关闭',
                entry: './DatePicker/maskClosable/index',
                displayCode: './DatePicker/maskClosable/Demo',
            },
        ],
    },
    {
        key: 'Toast',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: 'Toast基础用法：纯文字，加载/成功/失败状态',
                entry: './Toast/base/index',
                displayCode: './Toast/base/Demo',
            },
            {
                key: 'Position',
                title: '自定义位置',
                description: 'position属性使用',
                entry: './Toast/position/index',
                displayCode: './Toast/position/Demo',
            },
            {
                key: 'CustomStyle',
                title: '自定义样式',
                description: '内容以及遮罩层的自定义样式',
                entry: './Toast/customStyle/index',
                displayCode: './Toast/customStyle/Demo',
            },
            {
                key: 'ManulClose',
                title: '手动关闭',
                description: '调用组件close方法手动关闭toast',
                entry: './Toast/manulClose/index',
                displayCode: './Toast/manulClose/Demo',
            },
            {
                key: 'MaxLines',
                title: '最大行数',
                description: '最大行数',
                entry: './Toast/maxLines/index',
                displayCode: './Toast/maxLines/Demo',
            },
        ],
    },
    {
        key: 'Tabs',
        children: [
            {
                key: 'Base',
                title: '基础示例',
                description: 'Tabs的基本使用',
                entry: './Tabs/Base/index',
                displayCode: './Tabs/base/Demo',
            },
            {
                key: 'DivideEqually',
                title: '等分类型',
                description: 'Tabs的间距平分整个内容区域',
                entry: './Tabs/divideEqually/index',
                displayCode: './Tabs/divideEqually/Demo',
            },
            {
                key: 'FixedSpacing',
                title: '固定间距',
                description: 'Tabs的间距为固定宽度',
                entry: './Tabs/fixedSpacing/index',
                displayCode: './Tabs/fixedSpacing/Demo',
            },
            {
                key: 'CardTab',
                title: '卡片类型',
                description: '卡片类型的Tab',
                entry: './Tabs/cardTab/index',
                displayCode: './Tabs/cardTab/Demo',
            },
            {
                key: 'CardWithArrow',
                title: '选项卡类型',
                description: '选项卡类型Tabs',
                entry: './Tabs/cardWithArrow/index',
                displayCode: './Tabs/cardWithArrow/Demo',
            },
        ],
    },
    {
        key: 'Loading',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: 'loading是否展示，loading文字内容',
                entry: './Loading/base/index',
                displayCode: './Loading/base/Demo',
            },
            {
                key: 'Icon',
                title: '图标类型及自定义颜色',
                description: 'loading iconType图标类型配置，color自定义颜色',
                entry: './Loading/icon/index',
                displayCode: './Loading/icon/Demo',
            },
            {
                key: 'Fullscreen',
                title: '全屏',
                description: 'loading是否全屏',
                entry: './Loading/fullscreen/index',
                displayCode: './Loading/fullscreen/Demo',
            },
            {
                key: 'Vertical',
                title: '垂直排列',
                description: 'loading是否垂直排列',
                entry: './Loading/vertical/index',
                displayCode: './Loading/vertical/Demo',
            },
            {
                key: 'Indicator',
                title: '自定义图标',
                description: 'loading除固定图标外的自定义图标',
                entry: './Loading/indicator/index',
                displayCode: './Loading/indicator/Demo',
            },
            {
                key: 'CustomStyle',
                title: '自定义样式',
                description: 'loading自定义mask样式',
                entry: './Loading/customStyle/index',
                displayCode: './Loading/customStyle/Demo',
            },
        ],
    },
    {
        key: 'Switch',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: 'Switch组件基础用法',
                entry: './Switch/base/index',
                displayCode: './Switch/base/Demo',
            },
            {
                key: 'ManualSetValue',
                title: '受控用法',
                description: 'Switch受控用法',
                entry: './Switch/manualSetValue/index',
                displayCode: './Switch/manualSetValue/Demo',
            },
        ],
    },
    {
        key: 'ActionSheet',
        children: [
            {
                key: 'basic',
                title: '基础使用',
                description: '基础使用',
                entry: './ActionSheet/basic/index',
                displayCode: './ActionSheet/basic/Demo',
            },
            {
                key: 'callback',
                title: '各种回调',
                description: '各种回调',
                entry: './ActionSheet/callback/index',
                displayCode: './ActionSheet/callback/Demo',
            },
        ],
    },
    {
        key: 'Popover',
        children: [
            {
                key: 'basic',
                title: '基础用法',
                description: '基础用法',
                entry: './Popover/basic/index',
                displayCode: './Popover/basic/Demo',
            },
            {
                key: 'position',
                title: '设置菜单弹出位置',
                description: '支持 top, topLeft, topRight, bottom, bottomLeft, bottomRight',
                entry: './Popover/position/index',
                displayCode: './Popover/position/Demo',
            },
            {
                key: 'colorMode',
                title: '颜色模式',
                description: '默认为深色模式，可以通过 mode 参数设置',
                entry: './Popover/colorMode/index',
                displayCode: './Popover/colorMode/Demo',
            },
            {
                key: 'customizeRender',
                title: '自定义渲染菜单',
                description: '自定义渲染菜单内容',
                entry: './Popover/customizeRender/index',
                displayCode: './Popover/customizeRender/Demo',
            },
            {
                key: 'customizeStyle',
                title: '自定义样式',
                description: '自定义样式',
                entry: './Popover/customizeStyle/index',
                displayCode: './Popover/customizeStyle/Demo',
            },
        ],
    },
    {
        key: 'CalendarPicker',
        children: [
            {
                key: 'singleDate',
                title: '选择单个日期',
                description: '选择单个日期',
                entry: './CalendarPicker/singleDate/index',
                displayCode: './CalendarPicker/singleDate/Demo',
            },
            {
                key: 'dateRange',
                title: '选择日期范围',
                description: '选择开始和结束日期',
                entry: './CalendarPicker/dateRange/index',
                displayCode: './CalendarPicker/dateRange/Demo',
            },
            {
                key: 'multiDate',
                title: '日期多选',
                description: '选择多个日期',
                entry: './CalendarPicker/multiDate/index',
                displayCode: './CalendarPicker/multiDate/Demo',
            },
            {
                key: 'weekSelect',
                title: '选择周',
                description: '选择单个日期',
                entry: './CalendarPicker/weekSelect/index',
                displayCode: './CalendarPicker/weekSelect/Demo',
            },
            {
                key: 'customize',
                title: '自定义设置',
                description: '自定义设置',
                entry: './CalendarPicker/customize/index',
                displayCode: './CalendarPicker/customize/Demo',
            },
            {
                key: 'combination',
                title: '主要功能',
                description: '主要功能',
                entry: './CalendarPicker/combination/index',
                displayCode: './CalendarPicker/combination/Demo',
            },
        ],
    },
    {
        key: 'Selector',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: '支持基础的单选，使用`defalurValue`可以设置默认选中项',
                entry: './Selector/base/index',
                displayCode: './Selector/base/Demo',
            },
            {
                key: 'Config',
                title: '主要功能',
                description: '勾选对应项，配置对应功能展示。若要实现单独功能，请参考其余示例。',
                entry: './Selector/config/index',
                displayCode: './Selector/config/Demo',
            },
            {
                key: 'Multiple',
                title: '多选用法',
                description: '通过`multiple`属性，可以设置多选',
                entry: './Selector/multiple/index',
                displayCode: './Selector/multiple/Demo',
            },
            {
                key: 'customIcon',
                title: '前后缀图标',
                description: '通过 `prefixIcon`和`suffixIcon` 属性设置选项前后缀图标，支持 Icon 组件里的所有图标。',
                entry: './Selector/customIcon/index',
                displayCode: './Selector/customIcon/Demo',
            },
            {
                key: 'Disabled',
                title: '禁用选项',
                description: '通过 `disabled` 属性来禁用选项，禁用状态下按钮不可点击。单个选项禁用，可以配置`options`的 `disabled` 属性。',
                entry: './Selector/disabled/index',
                displayCode: './Selector/disabled/Demo',
            },
            {
                key: 'Size',
                title: '选项尺寸',
                description: '通过 size 设置按钮大小，支持 `large` | `normal` | `small` 三种尺寸，默认为 normal。',
                entry: './Selector/size/index',
                displayCode: './Selector/size/Demo',
            },
            {
                key: 'Type',
                title: '内置主题样式',
                description: '通过 type 设置内置主题样式，支持 `panel` | `line` | `weaken` 三种主题，默认 `panel`',
                entry: './Selector/type/index',
                displayCode: './Selector/type/Demo',
            },
            {
                key: 'ShowCheckMark',
                title: '展示选中对钩',
                description: '通过`showCheckMark`属性可以设置选中自动打钩',
                entry: './Selector/showCheckMark/index',
                displayCode: './Selector/showCheckMark/Demo',
            },
            {
                key: 'Arrow',
                title: '展示有箭头',
                description: '通过`arrow`属性可以设置气泡框类型选项',
                entry: './Selector/arrow/index',
                displayCode: './Selector/arrow/Demo',
            },
            {
                key: 'FieldNames',
                title: '自定义数据映射',
                description: '通过`fieldNames`属性，可以设置`options`数据结构的别名映射',
                entry: './Selector/fieldNames/index',
                displayCode: './Selector/fieldNames/Demo',
            },
            {
                key: 'Style',
                title: '自定义样式',
                description: '通过`style` `className`属性，可以自定义选项样式',
                entry: './Selector/style/index',
                displayCode: './Selector/style/Demo',
            },
        ],
    },
    {
        key: 'Form',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: '基本的表单数据域控制展示，包含布局、初始化、验证、提交。',
                entry: './Form/base/index',
                displayCode: './Form/base/Demo',
            },
            {
                key: 'Integrated',
                title: '综合示例',
                description: '复杂一点的控件',
                entry: './Form/integrated/index',
                displayCode: './Form/integrated/Demo',
            },
            {
                key: 'UseForm',
                title: '表单方法调用',
                description: '通过 Form.useForm 对表单数据域进行交互。',
                entry: './Form/useForm/index',
                displayCode: './Form/useForm/Demo',
            },
            {
                key: 'Disabled',
                title: '表单禁用',
                description: '设置表单组件禁用',
                entry: './Form/disabled/index',
                displayCode: './Form/disabled/Demo',
            },
            {
                key: 'LabelWrap',
                title: '表单标签可换行',
                description: '使用 labelWrap 可以开启 label 换行。',
                entry: './Form/labelWrap/index',
                displayCode: './Form/labelWrap/Demo',
            },
            {
                key: 'ValidateTrigger',
                title: '校验时机',
                description: '对于有异步校验的场景，过于频繁的校验会导致后端压力。可以通过 validateTrigger 改变校验时机，或者 validateDebounce 改变校验频率，或者 validateFirst 设置校验短路。',
                entry: './Form/validateTrigger/index',
                displayCode: './Form/validateTrigger/Demo',
            },
            {
                key: 'FormList',
                title: '复杂的动态增减表单项',
                description: '多个 Form.List 嵌套的使用场景。 validateTrigger 改变校验时机，或者 validateDebounce 改变校验频率，或者 validateFirst 设置校验短路。',
                entry: './Form/formList/index',
                displayCode: './Form/formList/Demo',
            },
            {
                key: 'Custom',
                title: '自定义展示区域',
                description: '可通过 arrow 属性展示或者自定义右侧箭头',
                entry: './Form/custom/index',
                displayCode: './Form/custom/Demo',
            },
            {
                key: 'Transparent',
                title: '背景透明',
                description: '表单背景透明',
                entry: './Form/transparent/index',
                displayCode: './Form/transparent/Demo',
            },
        ],
    },
    {
        key: 'SideBar',
        children: [
            {
                key: 'default',
                title: '基础用法',
                description: '侧边栏的基础使用',
                entry: './SideBar/default/index',
                displayCode: './SideBar/default/Demo',
            },
            {
                key: 'disabled',
                title: '禁用',
                description: '通过`options`中元素的`disabled`属性，可以设置选项的禁用，当一个父节点的所有子节点都设置了禁用，这个父节点也会被禁用',
                entry: './SideBar/disabled/index',
                displayCode: './SideBar/disabled/Demo',
            },
            {
                key: 'fieldname',
                title: '自定义数据映射',
                description: '通过`fieldNames`属性，可以设置`options`数据结构的别名映射',
                entry: './SideBar/fieldname/index',
                displayCode: './SideBar/fieldname/Demo',
            },
            {
                key: 'custom',
                title: '自定义样式',
                description: '通过`style` `className`属性，可以自定义侧边栏的样式',
                entry: './SideBar/custom/index',
                displayCode: './SideBar/custom/Demo',
            },
            {
                key: 'control',
                title: '受控模式',
                description: '设置`activeKey`后，组件变成受控模式，需要外部更新`activeKey`才能实现侧边栏的更新',
                entry: './SideBar/control/index',
                displayCode: './SideBar/control/Demo',
            },
            {
                key: 'longlist',
                title: '长列表',
                description: '两边滚动独立',
                entry: './SideBar/longlist/index',
                displayCode: './SideBar/longlist/Demo',
            },
        ],
    },
    {
        key: 'Refresh',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: '展示基础的 Refresh 组件用法，包括下拉刷新和加载更多。',
                entry: './Refresh/base/index',
                displayCode: './Refresh/base/Demo',
            },
            {
                key: 'CustomTexts',
                title: '自定义文案',
                description: '通过自定义文案属性，设置不同状态下的显示文本。',
                entry: './Refresh/customTexts/index',
                displayCode: './Refresh/customTexts/Demo',
            },
            {
                key: 'LoadFailedText',
                title: '加载失败',
                description: '模拟加载更多数据失败的情况。',
                entry: './Refresh/loadFailedText/index',
                displayCode: './Refresh/loadFailedText/Demo',
            },
            {
                key: 'Threshold',
                title: '阈值设置',
                description: '通过 threshold 属性设置触发加载更多的距离阈值。',
                entry: './Refresh/threshold/index',
                displayCode: './Refresh/threshold/Demo',
            },
            {
                key: 'HasMore',
                title: '是否有更多',
                description: '通过 hasMore 属性控制是否有更多数据可以加载。',
                entry: './Refresh/hasMore/index',
                displayCode: './Refresh/hasMore/Demo',
            },
        ],
    },
    {
        key: 'Checkbox',
        children: [
            {
                key: 'default',
                title: '基础用法',
                description: '多选框的基础使用',
                entry: './Checkbox/default/index',
                displayCode: './Checkbox/default/Demo',
            },
            {
                key: 'disabled',
                title: '禁用',
                description: '通过disabled来设置checkbox 不可用',
                entry: './Checkbox/disabled/index',
                displayCode: './Checkbox/disabled/Demo',
            },
            {
                key: 'other',
                title: '其他用法',
                description: '通过`labelPosition` `icon`属性，可以自定义多选框的特殊样式',
                entry: './Checkbox/other/index',
                displayCode: './Checkbox/other/Demo',
            },
            {
                key: 'control',
                title: '受控模式',
                description: '通过`checked`属性来 联动 checkbox',
                entry: './Checkbox/control/index',
                displayCode: './Checkbox/control/Demo',
            },
            {
                key: 'group',
                title: '选项组',
                description: 'options生成选项组，使用`indeterminate`实现全选效果',
                entry: './Checkbox/group/index',
                displayCode: './Checkbox/group/Demo',
            },
            {
                key: 'theme',
                title: '主题',
                description: '多选框样式主题',
                entry: './Checkbox/theme/index',
                displayCode: './Checkbox/theme/Demo',
            },
        ],
    },
    {
        key: 'Upload',
        children: [
            {
                key: 'base',
                title: '基础使用',
                description: '尝试上传几张图片，可以看到上传中和失败的效果',
                entry: './Upload/base/index',
                displayCode: './Upload/base/Demo',
            },
            {
                key: 'beforeUpload',
                title: '上传前拦截',
                description: '当用户选择的文件超过 1M 时，跳过上传并提示用户',
                entry: './Upload/beforeUpload/index',
                displayCode: './Upload/beforeUpload/Demo',
            },
            {
                key: 'custom',
                title: '自定义元素',
                description: '自定义上传组件的各个模块',
                entry: './Upload/custom/index',
                displayCode: './Upload/custom/Demo',
            },
            {
                key: 'ref',
                title: 'ref的使用',
                description: '可以通过ref获取Upload组件内的input元素',
                entry: './Upload/ref/index',
                displayCode: './Upload/ref/Demo',
            },
            {
                key: 'videoUpload',
                title: '视频上传',
                description: '使用 customImageRender 自定义图片及预览逻辑',
                entry: './Upload/videoUpload/index',
                displayCode: './Upload/videoUpload/Demo',
            },
            {
                key: 'propConfig',
                title: '自定义样式以及相关配置',
                description: '自定义样式className以及上传相关配置',
                entry: './Upload/propConfig/index',
                displayCode: './Upload/propConfig/Demo',
            },
        ],
    },
    {
        key: 'Image',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: 'Image支持自定义placeloader和fallback，默认placeholder为true, 加载中和失败都展示默认icon',
                entry: './Image/base/index',
                displayCode: './Image/base/Demo',
            },
            {
                key: 'fallback',
                title: '容错处理',
                description: '图片加载失败可自定义展示内容',
                entry: './Image/fallback/index',
                displayCode: './Image/fallback/Demo',
            },
            {
                key: 'fit',
                title: '填充模式',
                description: 'Image支持不同的填充模式，默认为fill',
                entry: './Image/fit/index',
                displayCode: './Image/fit/Demo',
            },
            {
                key: 'config',
                title: '主要功能',
                description: '勾选对应配置项，添加对应属性。',
                entry: './Image/config/index',
                displayCode: './Image/config/Demo',
            },
            {
                key: 'round',
                title: '圆形展示',
                description: '图片可展示为圆形',
                entry: './Image/round/index',
                displayCode: './Image/round/Demo',
            },
            {
                key: 'event',
                title: '事件处理',
                description: 'Image的onClick和onError',
                entry: './Image/event/index',
                displayCode: './Image/event/Demo',
            },
            {
                key: 'lazy',
                title: '懒加载',
                description: '支持懒加载',
                entry: './Image/lazy/index',
                displayCode: './Image/lazy/Demo',
            },
            {
                key: 'preview',
                title: '自定义预览',
                description: '可以自定义预览内容和样式',
                entry: './Image/preview/index',
                displayCode: './Image/preview/Demo',
            },
            {
                key: 'previewVisible',
                title: '单图预览的显隐控制',
                description: '图片默认可预览，当preview为false时不可预览，但也可以通过preview.visible控制预览图的显隐',
                entry: './Image/previewVisible/index',
                displayCode: './Image/previewVisible/Demo',
            },
            {
                key: 'previewGroup',
                title: '多图预览',
                description: '预览图片是通过Image组件注册的，Image.PreviewGroup的items优先级更高，当items有值时，Image不会出现在预览图列表里。另外还可以自定义预览内容和样式',
                entry: './Image/previewGroup/index',
                displayCode: './Image/previewGroup/Demo',
            },
            {
                key: 'previewItems',
                title: '受控预览',
                description: '点击按钮打开预览，并指定开始的图片序号。当Image.PreviewGroup不是作为Image的父级的时候，需要通过visible控制图片预览的显示',
                entry: './Image/previewItems/index',
                displayCode: './Image/previewItems/Demo',
            },
            {
                key: 'clearImageWidth',
                title: 'flex图片宽度压缩',
                description: 'flex布局中，图片宽度被文本内容压缩问题解决',
                entry: './Image/clearImageWidth/index',
                displayCode: './Image/clearImageWidth/Demo',
            },
        ],
    },
    {
        key: 'LazyLoad',
        children: [
            {
                key: 'base',
                title: '基本用法',
                description: 'LazyLoad默认展示空白',
                entry: './LazyLoad/base/index',
                displayCode: './LazyLoad/base/Demo',
            },
            {
                key: 'custom',
                title: '自定义占位视图',
                description: 'LazyLoad可自定义占位视图',
                entry: './LazyLoad/custom/index',
                displayCode: './LazyLoad/custom/Demo',
            },
        ],
    },
    {
        key: 'Steps',
        children: [
            {
                key: 'demoSample',
                title: '基本用法',
                description: '简单的步骤条',
                entry: './Steps/demoSample/index',
                displayCode: './Steps/demoSample/Demo',
            },
            {
                key: 'vertical',
                title: '竖直方向的步骤条',
                description: '简单的竖直方向的步骤条',
                entry: './Steps/vertical/index',
                displayCode: './Steps/vertical/Demo',
            },
            {
                key: 'dot',
                title: '点状步骤条',
                description: '包含步骤点的进度条，横向和竖向都支持，也支持状态',
                entry: './Steps/dot/index',
                displayCode: './Steps/dot/Demo',
            },
            {
                key: 'config',
                title: '主要功能',
                description: '勾选对应配置项，可添加对应属性。通过设置items的icon属性，可以启用自定义图标。传入string类型按Icon的name对待，且颜色和默认的相应状态保持一致',
                entry: './Steps/config/index',
                displayCode: './Steps/config/Demo',
            },
            {
                key: 'click',
                title: '可点击',
                description: '设置onChange后，Steps变为可点击状态，结合disabled可以禁用某个步骤的点击',
                entry: './Steps/click/index',
                displayCode: './Steps/click/Demo',
            },
            {
                key: 'status',
                title: '带状态的步骤',
                description: 'Stesps的status指定当前步骤的状态，也可以通过items的status指定每一步的状态',
                entry: './Steps/status/index',
                displayCode: './Steps/status/Demo',
            },
            {
                key: 'label',
                title: 'icon左侧内容自定义',
                description: '垂直状态下可添加icon左侧自定义内容',
                entry: './Steps/label/index',
                displayCode: './Steps/label/Demo',
            },
        ],
    },
    {
        key: 'Cascader',
        children: [
            {
                key: 'default',
                title: '基础用法',
                description: '基础使用',
                entry: './Cascader/default/index',
                displayCode: './Cascader/default/Demo',
            },
            {
                key: 'multiple',
                title: '多选用法',
                description: '多选使用',
                entry: './Cascader/multiple/index',
                displayCode: './Cascader/multiple/Demo',
            },
            {
                key: 'load',
                title: '异步加载用法',
                description: '异步加载使用',
                entry: './Cascader/load/index',
                displayCode: './Cascader/load/Demo',
            },
            {
                key: 'form',
                title: 'form与级联配合用法',
                description: 'form与级联配合用法',
                entry: './Cascader/form/index',
                displayCode: './Cascader/form/Demo',
            },
            {
                key: 'other',
                title: '自定义多选图标',
                description: '通过iconRender属性和css来配合实现自定义图标',
                entry: './Cascader/other/index',
                displayCode: './Cascader/other/Demo',
            },
            {
                key: 'search',
                title: '搜索功能',
                description: '通过showSearch来配置搜索功能',
                entry: './Cascader/search/index',
                displayCode: './Cascader/search/Demo',
            },
        ],
    },
    {
        key: 'SafeArea',
        children: [
            {
                key: 'demoSample',
                title: '基础用法',
                description: '根据设置`position`来了默认设置顶部或底部安全区域。注：只针对ios机型生效，安卓如有需要场景需要自行设置',
                entry: './SafeArea/demoSample/index',
                displayCode: './SafeArea/demoSample/Demo',
            },
        ],
    },
    {
        key: 'Divider',
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: 'Divider组件基础用法',
                entry: './Divider/base/index',
                displayCode: './Divider/base/Demo',
            },
        ],
    },
    {
        key: 'Empty',
        children: [
            {
                key: 'base',
                title: 'empty基本使用',
                description: 'Empty基本使用，提供了empty、nodata、disconnected、unauth和unopen五种空状态',
                entry: './Empty/base/index',
                displayCode: './Empty/base/Demo',
            },
            {
                key: 'fullPage',
                title: '整页样式',
                description: 'Empty整页样式',
                entry: './Empty/fullPage/index',
                displayCode: './Empty/fullPage/Demo',
            },
            {
                key: 'custom',
                title: '自定义样式',
                description: 'Empty自定义样式，imageStyle只有当image为string类型时生效',
                entry: './Empty/custom/index',
                displayCode: './Empty/custom/Demo',
            },
        ],
    },
    {
        key: 'Collapse',
        children: [
            {
                key: 'base',
                title: '基本用法',
                description: '折叠面板通用用法',
                entry: './Collapse/base/index',
                displayCode: './Collapse/base/Demo',
            },
            {
                key: 'control',
                title: '受控用法',
                description: '折叠面板受控用法',
                entry: './Collapse/control/index',
                displayCode: './Collapse/control/Demo',
            },
            {
                key: 'config',
                title: '主要功能',
                description: '勾选对应项，配置对应功能展示',
                entry: './Collapse/config/index',
                displayCode: './Collapse/config/Demo',
            },
        ],
    },
    {
        key: 'Badge',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: 'Badge基础用法',
                entry: './Badge/base/index',
                displayCode: './Badge/base/Demo',
            },
            {
                key: 'direct',
                title: '指向型角标',
                description: 'Badge指向型角标',
                entry: './Badge/direct/index',
                displayCode: './Badge/direct/Demo',
            },
            {
                key: 'config',
                title: '主要功能',
                description: '勾选对应项，配置对应功能展示',
                entry: './Badge/config/index',
                displayCode: './Badge/config/Demo',
            },
        ],
    },
    {
        key: 'Swiper',
        children: [
            {
                key: 'default',
                title: '默认使用',
                description: 'swiper 基本使用',
                entry: './Swiper/default/index',
                displayCode: './Swiper/default/Demo',
            },
            {
                key: 'control',
                title: '手动控制',
                description: '通过ref手动翻页',
                entry: './Swiper/control/index',
                displayCode: './Swiper/control/Demo',
            },
            {
                key: 'full',
                title: '不是满宽的滑块',
                description: '不是满宽的滑块',
                entry: './Swiper/full/index',
                displayCode: './Swiper/full/Demo',
            },
            {
                key: 'indicator',
                title: '指示器',
                description: '自定义指示器场景',
                entry: './Swiper/indicator/index',
                displayCode: './Swiper/indicator/Demo',
            },
            {
                key: 'autoHeight',
                title: '高度自适应',
                description: '高度自适应',
                entry: './Swiper/autoHeight/index',
                displayCode: './Swiper/autoHeight/Demo',
            },
            {
                key: 'vertical',
                title: '垂直',
                description: '通过设置  dotPosition 可以指定滚动方向（目前仅支持 right｜bottom），垂直方向的滚动需要手动设置容器高度，否则会展示异常',
                entry: './Swiper/vertical/index',
                displayCode: './Swiper/vertical/Demo',
            },
             {
                key: 'theme',
                title: '主题',
                description: '自定义修改主题配置',
                entry: './Swiper/theme/index',
                displayCode: './Swiper/theme/Demo',
            },
        ],
    },
    {
        key: 'Rate',
        children: [
            {
                key: 'default',
                title: '默认使用',
                description: 'Rate组件的基础用法',
                entry: './Rate/default/index',
                displayCode: './Rate/default/Demo',
            },
        ],
    },
    {
        key: 'Grid',
        children: [
            {
                key: 'demoSample',
                title: '基础示例',
                description: 'Grid的基本用法',
                entry: './Grid/demoSample/index',
                displayCode: './Grid/demoSample/Demo',
            },
            {
                key: 'gutter',
                title: '区块间隔',
                description: '栅格常常需要和间隔进行配合，你可以使用`Row`的`gutter`属性。如果需要垂直间距，可以写成数组形式`[水平间距, 垂直间距]`',
                entry: './Grid/gutter/index',
                displayCode: './Grid/gutter/Demo',
            },
            {
                key: 'offset',
                title: '左右偏移',
                description: '使用`offset`可以将列向右侧偏。例如，`offset={4}`将元素向右侧偏移了4个列的宽度',
                entry: './Grid/offset/index',
                displayCode: './Grid/offset/Demo',
            },
            {
                key: 'pull',
                title: '栅格排序',
                description: '通过使用`push`和`pull`类就可以很容易的改变列的顺序',
                entry: './Grid/pull/index',
                displayCode: './Grid/pull/Demo',
            },
            {
                key: 'justify',
                title: '排版',
                description: '子元素根据不同的值`start`、`center`、`end`、`space-between`、`space-around`和`space-evenly`，分别定义其在父节点里面的排版方式',
                entry: './Grid/justify/index',
                displayCode: './Grid/justify/Demo',
            },
            {
                key: 'align',
                title: '对齐',
                description: '子元素垂直对齐方式',
                entry: './Grid/align/index',
                displayCode: './Grid/align/Demo',
            },
            {
                key: 'flex',
                title: 'flex填充',
                description: '通过`flex`来设置栅格的占位',
                entry: './Grid/flex/index',
                displayCode: './Grid/flex/Demo',
            },
            {
                key: 'order',
                title: '排序',
                description: '通过`order`来改变元素的排序',
                entry: './Grid/order/index',
                displayCode: './Grid/order/Demo',
            },
            {
                key: 'config',
                title: '主要功能',
                description: '勾选对应项，配置对应功能展示',
                entry: './Grid/config/index',
                displayCode: './Grid/config/Demo',
            },
        ],
    },
    {
        key: 'Skeleton',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: '最简单的占位效果。',
                entry: './Skeleton/base/index',
                displayCode: './Skeleton/base/Demo',
            },
            {
                key: 'active',
                title: '动画效果',
                description: '显示动画效果。',
                entry: './Skeleton/active/index',
                displayCode: './Skeleton/active/Demo',
            },
            {
                key: 'complex',
                title: '复杂组合',
                description: '更复杂的组合。',
                entry: './Skeleton/complex/index',
                displayCode: './Skeleton/complex/Demo',
            },
            {
                key: 'children',
                title: '包含子组件',
                description: '加载占位图包含子组件。',
                entry: './Skeleton/children/index',
                displayCode: './Skeleton/children/Demo',
            },
            {
                key: 'avatar',
                title: '头像',
                description: '在列表组件中使用头像加载占位符。',
                entry: './Skeleton/avatar/index',
                displayCode: './Skeleton/avatar/Demo',
            },
        ],
    },
    {
        key: 'Filter',
        children: [
            {
                key: 'base',
                title: '基础示例',
                description: 'Filter基本用法',
                entry: './Filter/base/index',
                displayCode: './Filter/base/Demo',
            },
            {
                key: 'other',
                title: '其他示例',
                description: 'Filter基本用法',
                entry: './Filter/other/index',
                displayCode: './Filter/other/Demo',
            },
        ],
    },
    {
        key: 'Progress',
        children: [
            {
                key: 'default',
                title: '基本用法',
                description: 'Progress支持line和circle两种进度条',
                entry: './Progress/default/index',
                displayCode: './Progress/default/Demo',
            },
            {
                key: 'size',
                title: '进度条尺寸',
                description: '进度条尺寸可自定义，line型预设default和small两种尺寸， circle类型的size只支持number类型。通过strokeWidth可调整进度条宽度',
                entry: './Progress/size/index',
                displayCode: './Progress/size/Demo',
            },
            {
                key: 'format',
                title: '自定义内容',
                description: '百分比展示可自定义',
                entry: './Progress/format/index',
                displayCode: './Progress/format/Demo',
            },
            {
                key: 'colors',
                title: '自定义颜色',
                description: '进度条的颜色可自定义，且支持渐变色；底色也可以自定义',
                entry: './Progress/colors/index',
                displayCode: './Progress/colors/Demo',
            },
            {
                key: 'animate',
                title: '进度条动效',
                description: '进度条的动画效果',
                entry: './Progress/animate/index',
                displayCode: './Progress/animate/Demo',
            },
            {
                key: 'lincap',
                title: '进度条的样式',
                description: '进度条默认round，可改为butt，square',
                entry: './Progress/lincap/index',
                displayCode: './Progress/lincap/Demo',
            },
        ],
    },
    {
        key: 'Stepper',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: '步进器的基础使用',
                entry: './Stepper/base/index',
                displayCode: './Stepper/base/Demo',
            },
            {
                key: 'type',
                title: '样式类型',
                description: '步进器样式类型',
                entry: './Stepper/type/index',
                displayCode: './Stepper/type/Demo',
            },
            {
                key: 'style',
                title: '自定义',
                description: '自定义',
                entry: './Stepper/style/index',
                displayCode: './Stepper/style/Demo',
            },
        ],
    },
    {
        key: 'SwipeAction',
        children: [
            {
                key: 'default',
                title: '默认使用方法',
                description: 'SwipeAction基本使用',
                entry: './SwipeAction/default/index',
                displayCode: './SwipeAction/default/Demo',
            },
            {
                key: 'custom',
                title: '用户自定义样式修改',
                description: 'SwipeAction样式',
                entry: './SwipeAction/custom/index',
                displayCode: './SwipeAction/custom/Demo',
            },
            {
                key: 'ref',
                title: '受控模式',
                description: '使用 ref 手动控制归位逻辑',
                entry: './SwipeAction/ref/index',
                displayCode: './SwipeAction/ref/Demo',
            },
        ],
    },
    {
        key: 'Segmented',
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: 'Segmented组件最基本用法。',
                entry: './Segmented/base/index',
                displayCode: './Segmented/base/Demo',
            },
            {
                key: 'block',
                title: 'block属性',
                description: 'block 属性使组件适合父元素宽度。',
                entry: './Segmented/block/index',
                displayCode: './Segmented/block/Demo',
            },
            {
                key: 'disabled',
                title: 'disabled属性',
                description: '禁用整个组件或其中单项',
                entry: './Segmented/disabled/index',
                displayCode: './Segmented/disabled/Demo',
            },
            {
                key: 'size',
                title: '大小',
                description: 'Segemented组件提供了两种尺寸：large、small',
                entry: './Segmented/size/index',
                displayCode: './Segmented/size/Demo',
            },
            {
                key: 'icon',
                title: 'icon属性',
                description: '给单项设置Icon。',
                entry: './Segmented/icon/index',
                displayCode: './Segmented/icon/Demo',
            },
            {
                key: 'custom',
                title: '自定义label',
                description: '使用 ReactNode 自定义渲染每一个单项。',
                entry: './Segmented/custom/index',
                displayCode: './Segmented/custom/Demo',
            },
        ],
    },
    {
        key: 'Avatar',
        children: [
            {
                key: 'Base',
                title: '基础用法',
                description: '头像支持两种形状，支持传入number定义大小，且设有默认占位图。',
                entry: './Avatar/base/index',
                displayCode: './Avatar/base/Demo',
            },
            {
                key: 'Badge',
                title: '带徽标的头像',
                description: '通常用于消息提示。',
                entry: './Avatar/badge/index',
                displayCode: './Avatar/badge/Demo',
            },
            {
                key: 'Type',
                title: '类型',
                description: '支持三种类型：Image、Icon 以及字符。其中，Avatar组件不会对传入的Icon的大小做出控制，用户可以自行设置以达到想要的效果。',
                entry: './Avatar/type/index',
                displayCode: './Avatar/type/Demo',
            },
            {
                key: 'Children',
                title: '自动调整字符大小',
                description: '对于字符型的头像，当字符串较长时，字体大小可以根据头像宽度自动调整。也可使用 gap 来设置字符距离左右两侧边界单位像素。',
                entry: './Avatar/children/index',
                displayCode: './Avatar/children/Demo',
            },
            {
                key: 'Style',
                title: '自定义样式',
                description: '通过`style` `className`属性，可以自定义样式',
                entry: './Avatar/style/index',
                displayCode: './Avatar/style/Demo',
            },
            {
                key: 'OnError',
                title: '关闭默认占位图',
                description: '当onError返回false时，关闭组件的默认fallback行为，此时可以使用icon或children作为默认占位图。',
                entry: './Avatar/onError/index',
                displayCode: './Avatar/onError/Demo',
            },
            {
                key: 'Priority',
                title: '头像元素优先级',
                description: '优先级 src - icon - children - alt - 默认占位图',
                entry: './Avatar/priority/index',
                displayCode: './Avatar/priority/Demo',
            },
            {
                key: 'fit',
                title: '设置图片fit',
                description: '设置头像fit属性',
                entry: './Avatar/fit/index',
                displayCode: './Avatar/fit/Demo',
            },
        ],
    },
    {
        key: 'VideoPlayer',
        noDemo: false,
        children: [
            {
                key: 'demoSample',
                title: '基本用法',
                description: '视频播放基本用法，本示例是一个竖屏的视频，开启了循环播放',
                entry: './VideoPlayer/demoSample/index',
                displayCode: './VideoPlayer/demoSample/Demo',
            },
            {
                key: 'landscape',
                title: '横屏视频',
                description: '视频分辨率宽高比大于1的情况，全屏时为横屏模式(注意：H5页面中无法控制设备的物理旋转，如果需要，可通过调用KNB等方式处理)。此示例还包括1、设置宽高比，开始播放时间，自动播放，当需要自动播放时，需要静音，muted为true',
                entry: './VideoPlayer/landscape/index',
                displayCode: './VideoPlayer/landscape/Demo',
            },
            {
                key: 'fullscreen',
                title: '初始化全屏模式',
                description: '初始化全屏模式',
                entry: './VideoPlayer/fullscreen/index',
                displayCode: './VideoPlayer/fullscreen/Demo',
            },
            {
                key: 'custom',
                title: '自定义控制栏',
                description: '控制栏的播放、时间显示、倍速、全屏都可自定义显示。此示例还展示了宽高的设置，当需要指定宽高时，responsive需要设置为false',
                entry: './VideoPlayer/custom/index',
                displayCode: './VideoPlayer/custom/Demo',
            },
            {
                key: 'footerBar',
                title: '自定义footerBar',
                description: '控制栏下方可自定义一个footerBar，显示也受controlBar的autoHide控制',
                entry: './VideoPlayer/footerBar/index',
                displayCode: './VideoPlayer/footerBar/Demo',
            },
        ],
    },
    {
        key: 'PickerView',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: '选择器视图的基础用法',
                entry: './PickerView/base/index',
                displayCode: './PickerView/base/Demo',
            },
            {
                key: 'controlled',
                title: '受控',
                description: '受控模式',
                entry: './PickerView/controlled/index',
                displayCode: './PickerView/controlled/Demo',
            },
        ],
    },
    {
        key: 'Picker',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: '选择器的基础用法',
                entry: './Picker/base/index',
                displayCode: './Picker/base/Demo',
            },
            {
                key: 'children',
                title: '渲染所选值',
                description: '在 children 渲染函数中，你可以使用第二个参数 actions 来非常方便的控制 Picker 的显示或隐藏',
                entry: './Picker/children/index',
                displayCode: './Picker/children/Demo',
            },
            {
                key: 'prompt',
                title: '指令式调用',
                description: '指令式调用，返回值为所选项',
                entry: './Picker/prompt/index',
                displayCode: './Picker/prompt/Demo',
            },
            {
                key: 'custom',
                title: '自定义',
                description: '你可以通过 CSS 变量对 Picker 的字号等进行自定义',
                entry: './Picker/custom/index',
                displayCode: './Picker/custom/Demo',
            },
            {
                key: 'lazy',
                title: '懒加载',
                description: '你可以在 Picker 显示时发起异步请求获取数据，默认提供了转圈加载中的加载效果，你也可以传入 loadingContent 自定义 loading 样式。',
                entry: './Picker/lazy/index',
                displayCode: './Picker/lazy/Demo',
            },
            {
                key: 'other',
                title: '其他用法',
                description: '其他可选参数展示',
                entry: './Picker/other/index',
                displayCode: './Picker/other/Demo',
            },
        ],
    },
    {
        key: 'Slider',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: '基础用法',
                entry: './Slider/base/index',
                displayCode: './Slider/base/Demo',
            },
            {
                key: 'custom',
                title: '自定义使用',
                description: '自定义使用',
                entry: './Slider/custom/index',
                displayCode: './Slider/custom/Demo',
            },
        ],
    },
    {
        key: 'Affix',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: '固钉的基础用法',
                entry: './Affix/base/index',
                displayCode: './Affix/base/Demo',
            },
        ],
    },
    {
        key: 'SnackBar',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: '基础用法',
                entry: './SnackBar/base/index',
                displayCode: './SnackBar/base/Demo',
            },
            {
                key: 'custom',
                title: '自定义样式',
                description: '自定义样式',
                entry: './SnackBar/customStyle/index',
                displayCode: './SnackBar/customStyle/Demo',
            },
        ],
    },
    {
        key: 'TabBar',
        noDemo: false,
        children: [
            {
                key: 'base',
                title: '基础用法',
                description: 'TabBar的一些基础用法',
                entry: './TabBar/base/index',
                displayCode: './TabBar/base/Demo',
            },
            {
                key: 'custom',
                title: '自定义样式',
                description: '自定义TabBar样式',
                entry: './TabBar/custom/index',
                displayCode: './TabBar/custom/Demo',
            },
        ],
    },
]
