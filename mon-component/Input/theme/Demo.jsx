import React, { useState } from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
import { Input } from '@roo/roo-b-mobile';
import '../index.less';

function Demo() {
    const [value, setValue] = useState('');
    return (
        <div className="input-container">
            <Input
                value={value}
                style={{
                    '--rbm-input-paading': '20px 18px',
                    '--rbm-input-height': '20px',
                    '--rbm-input-width': '50%',
                    '--rbm-input-line-height': '20px',
                    '--rbm-input-clear-padding-right': '20px',
                }}
                allowClear
                onChange={val => setValue(val)}
            />
            <br />
            <Input disabled style={{ '--rbm-input-disabled-opacity': '0.5' }} />
            <br />
            <Input
                prefix="http://"
                suffix=".com"
                placeholder="请输入"
                style={{
                    '--rbm-input-prefix-padding-right': '20px',
                }}
            />
            <br />
            <Input.TextArea
                allowClear
                style={{
                    '--rbm-input-textarea-padding': '60px',
                    '--rbm-input-textarea-clear-padding-right': '130px',
                    '--rbm-input-search-wrapper-padding': '50px',
                }}
            />
            <br />
            <Input.Search
                placeholder="请输入"
                style={{
                    '--rbm-input-search-input-height': '150px',
                    '--rbm-input-search-input-border-radius': '10px',
                }}
            />
        </div>
    );
}
export default Demo;
